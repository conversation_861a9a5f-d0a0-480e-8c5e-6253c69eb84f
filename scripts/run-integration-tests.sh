#!/bin/bash

# Integration Test Runner for xbit-dex Agent Service
# This script runs integration tests with proper setup and teardown

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_TIMEOUT=${TEST_TIMEOUT:-300s}
VERBOSE=${VERBOSE:-false}
PARALLEL=${PARALLEL:-1}

echo -e "${BLUE}=== xbit-dex Agent Integration Tests ===${NC}"
echo "Test timeout: $TEST_TIMEOUT"
echo "Verbose: $VERBOSE"
echo "Parallel: $PARALLEL"
echo ""

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_status $BLUE "Checking prerequisites..."
    
    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        print_status $RED "Error: Go is not installed"
        exit 1
    fi
    
    # Check Go version
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    print_status $GREEN "Go version: $GO_VERSION"
    
    # Check if we're in the right directory
    if [ ! -f "go.mod" ]; then
        print_status $RED "Error: go.mod not found. Please run from project root."
        exit 1
    fi
    
    print_status $GREEN "Prerequisites check passed"
}

# Function to setup test environment
setup_test_env() {
    print_status $BLUE "Setting up test environment..."
    
    # Set test environment variables
    export GO_ENV=test
    export TEST_MODE=integration
    
    # Create test directories if they don't exist
    mkdir -p tmp/test-data
    mkdir -p tmp/test-logs
    
    print_status $GREEN "Test environment setup complete"
}

# Function to cleanup test environment
cleanup_test_env() {
    print_status $BLUE "Cleaning up test environment..."
    
    # Clean up temporary test files
    rm -rf tmp/test-data
    rm -rf tmp/test-logs
    
    print_status $GREEN "Test environment cleanup complete"
}

# Function to run integration tests
run_integration_tests() {
    print_status $BLUE "Running integration tests..."
    
    # Build test flags
    TEST_FLAGS="-tags=integration"
    TEST_FLAGS="$TEST_FLAGS -timeout=$TEST_TIMEOUT"
    TEST_FLAGS="$TEST_FLAGS -parallel=$PARALLEL"
    
    if [ "$VERBOSE" = "true" ]; then
        TEST_FLAGS="$TEST_FLAGS -v"
    fi
    
    # Run tests with coverage
    TEST_PACKAGES="./internal/integration/..."
    
    print_status $YELLOW "Test command: go test $TEST_FLAGS $TEST_PACKAGES"
    echo ""
    
    if go test $TEST_FLAGS $TEST_PACKAGES; then
        print_status $GREEN "✅ All integration tests passed!"
        return 0
    else
        print_status $RED "❌ Some integration tests failed!"
        return 1
    fi
}

# Function to run specific test
run_specific_test() {
    local test_name=$1
    print_status $BLUE "Running specific test: $test_name"
    
    TEST_FLAGS="-tags=integration -v -timeout=$TEST_TIMEOUT"
    TEST_PACKAGES="./internal/integration/..."
    
    if go test $TEST_FLAGS -run "$test_name" $TEST_PACKAGES; then
        print_status $GREEN "✅ Test $test_name passed!"
        return 0
    else
        print_status $RED "❌ Test $test_name failed!"
        return 1
    fi
}

# Function to run tests with coverage
run_tests_with_coverage() {
    print_status $BLUE "Running integration tests with coverage..."
    
    COVERAGE_FILE="tmp/integration-coverage.out"
    mkdir -p tmp
    
    TEST_FLAGS="-tags=integration -timeout=$TEST_TIMEOUT -parallel=$PARALLEL"
    TEST_FLAGS="$TEST_FLAGS -coverprofile=$COVERAGE_FILE"
    TEST_FLAGS="$TEST_FLAGS -covermode=atomic"
    
    if [ "$VERBOSE" = "true" ]; then
        TEST_FLAGS="$TEST_FLAGS -v"
    fi
    
    TEST_PACKAGES="./internal/integration/..."
    
    if go test $TEST_FLAGS $TEST_PACKAGES; then
        print_status $GREEN "✅ Integration tests with coverage completed!"
        
        # Generate coverage report
        if [ -f "$COVERAGE_FILE" ]; then
            print_status $BLUE "Generating coverage report..."
            go tool cover -html=$COVERAGE_FILE -o tmp/integration-coverage.html
            print_status $GREEN "Coverage report generated: tmp/integration-coverage.html"
            
            # Show coverage summary
            COVERAGE_PERCENT=$(go tool cover -func=$COVERAGE_FILE | grep total | awk '{print $3}')
            print_status $YELLOW "Integration test coverage: $COVERAGE_PERCENT"
        fi
        
        return 0
    else
        print_status $RED "❌ Integration tests with coverage failed!"
        return 1
    fi
}

# Function to list available tests
list_tests() {
    print_status $BLUE "Available integration tests:"
    go test -tags=integration -list=. ./internal/integration/... | grep -E "^Test" | sort
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  run                 Run all integration tests (default)"
    echo "  coverage            Run tests with coverage report"
    echo "  list                List available tests"
    echo "  test <name>         Run specific test"
    echo "  help                Show this help message"
    echo ""
    echo "Options:"
    echo "  -v, --verbose       Enable verbose output"
    echo "  -t, --timeout       Set test timeout (default: 300s)"
    echo "  -p, --parallel      Set parallel test count (default: 1)"
    echo ""
    echo "Environment Variables:"
    echo "  VERBOSE             Enable verbose output (true/false)"
    echo "  TEST_TIMEOUT        Test timeout duration"
    echo "  PARALLEL            Number of parallel tests"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run all tests"
    echo "  $0 -v                                 # Run with verbose output"
    echo "  $0 coverage                           # Run with coverage"
    echo "  $0 test TestInvitationWorkflow        # Run specific test"
    echo "  $0 list                               # List all tests"
}

# Parse command line arguments
COMMAND="run"
while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--timeout)
            TEST_TIMEOUT="$2"
            shift 2
            ;;
        -p|--parallel)
            PARALLEL="$2"
            shift 2
            ;;
        run|coverage|list|help)
            COMMAND="$1"
            shift
            ;;
        test)
            COMMAND="test"
            TEST_NAME="$2"
            shift 2
            ;;
        *)
            print_status $RED "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    case $COMMAND in
        run)
            check_prerequisites
            setup_test_env
            trap cleanup_test_env EXIT
            run_integration_tests
            ;;
        coverage)
            check_prerequisites
            setup_test_env
            trap cleanup_test_env EXIT
            run_tests_with_coverage
            ;;
        test)
            if [ -z "$TEST_NAME" ]; then
                print_status $RED "Error: Test name required for 'test' command"
                show_help
                exit 1
            fi
            check_prerequisites
            setup_test_env
            trap cleanup_test_env EXIT
            run_specific_test "$TEST_NAME"
            ;;
        list)
            list_tests
            ;;
        help)
            show_help
            ;;
        *)
            print_status $RED "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main
