package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCommissionLedger_Struct(t *testing.T) {
	// Test that CommissionLedger struct can be created and has expected fields
	recipientID := uuid.New()
	sourceID := uuid.New()
	now := time.Now()

	ledger := &CommissionLedger{
		ID:                    uuid.New(),
		RecipientUserID:       recipientID,
		SourceUserID:          sourceID,
		SourceTransactionID:   "tx_123456",
		SourceTransactionType: "MEME",
		CommissionAmount:      decimal.NewFromFloat(1.5),
		CommissionAsset:       "SOL",
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
		ClaimedAt:             nil,
	}

	assert.NotEqual(t, uuid.Nil, ledger.ID)
	assert.Equal(t, recipientID, ledger.RecipientUserID)
	assert.Equal(t, sourceID, ledger.SourceUserID)
	assert.Equal(t, "tx_123456", ledger.SourceTransactionID)
	assert.Equal(t, "MEME", ledger.SourceTransactionType)
	assert.True(t, ledger.CommissionAmount.Equal(decimal.NewFromFloat(1.5)))
	assert.Equal(t, "SOL", ledger.CommissionAsset)
	assert.Equal(t, "PENDING_CLAIM", ledger.Status)
	assert.NotNil(t, ledger.CreatedAt)
	assert.NotNil(t, ledger.UpdatedAt)
	assert.Nil(t, ledger.ClaimedAt)
}

func TestCommissionLedger_TableName(t *testing.T) {
	ledger := CommissionLedger{}
	assert.Equal(t, "commission_ledger", ledger.TableName())
}

func TestCommissionLedger_TransactionTypes(t *testing.T) {
	validTransactionTypes := []string{"MEME", "CONTRACT"}
	
	for _, transactionType := range validTransactionTypes {
		t.Run("valid_transaction_type_"+transactionType, func(t *testing.T) {
			ledger := &CommissionLedger{
				SourceTransactionType: transactionType,
			}
			
			assert.Contains(t, validTransactionTypes, ledger.SourceTransactionType)
		})
	}
}

func TestCommissionLedger_Assets(t *testing.T) {
	validAssets := []string{"SOL", "USDC"}
	
	for _, asset := range validAssets {
		t.Run("valid_asset_"+asset, func(t *testing.T) {
			ledger := &CommissionLedger{
				CommissionAsset: asset,
			}
			
			assert.Contains(t, validAssets, ledger.CommissionAsset)
		})
	}
}

func TestCommissionLedger_Status(t *testing.T) {
	validStatuses := []string{"PENDING_CLAIM", "CLAIMED"}
	
	for _, status := range validStatuses {
		t.Run("valid_status_"+status, func(t *testing.T) {
			ledger := &CommissionLedger{
				Status: status,
			}
			
			assert.Contains(t, validStatuses, ledger.Status)
		})
	}
}

func TestCommissionLedger_DecimalPrecision(t *testing.T) {
	tests := []struct {
		name     string
		amount   string
		expected string
	}{
		{
			name:     "small amount with high precision",
			amount:   "0.000000000000000001",
			expected: "0.000000000000000001",
		},
		{
			name:     "large amount",
			amount:   "1000000.123456789012345678",
			expected: "1000000.123456789012345678",
		},
		{
			name:     "zero amount",
			amount:   "0",
			expected: "0",
		},
		{
			name:     "typical commission amount",
			amount:   "1.5",
			expected: "1.5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			amount, err := decimal.NewFromString(tt.amount)
			require.NoError(t, err)

			ledger := &CommissionLedger{
				CommissionAmount: amount,
			}

			assert.Equal(t, tt.expected, ledger.CommissionAmount.String())
		})
	}
}

func TestCommissionLedger_BusinessLogicValidation(t *testing.T) {
	tests := []struct {
		name        string
		ledger      *CommissionLedger
		description string
	}{
		{
			name: "MEME commission in SOL",
			ledger: &CommissionLedger{
				ID:                    uuid.New(),
				RecipientUserID:       uuid.New(),
				SourceUserID:          uuid.New(),
				SourceTransactionID:   "meme_tx_123",
				SourceTransactionType: "MEME",
				CommissionAmount:      decimal.NewFromFloat(0.5),
				CommissionAsset:       "SOL",
				Status:                "PENDING_CLAIM",
			},
			description: "MEME transaction commission paid in SOL",
		},
		{
			name: "CONTRACT commission in USDC",
			ledger: &CommissionLedger{
				ID:                    uuid.New(),
				RecipientUserID:       uuid.New(),
				SourceUserID:          uuid.New(),
				SourceTransactionID:   "contract_tx_456",
				SourceTransactionType: "CONTRACT",
				CommissionAmount:      decimal.NewFromFloat(10.25),
				CommissionAsset:       "USDC",
				Status:                "PENDING_CLAIM",
			},
			description: "CONTRACT transaction commission paid in USDC",
		},
		{
			name: "Claimed commission",
			ledger: &CommissionLedger{
				ID:                    uuid.New(),
				RecipientUserID:       uuid.New(),
				SourceUserID:          uuid.New(),
				SourceTransactionID:   "tx_789",
				SourceTransactionType: "MEME",
				CommissionAmount:      decimal.NewFromFloat(2.0),
				CommissionAsset:       "SOL",
				Status:                "CLAIMED",
				ClaimedAt:             &time.Time{},
			},
			description: "Commission that has been claimed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify basic structure
			require.NotNil(t, tt.ledger)
			assert.NotEqual(t, uuid.Nil, tt.ledger.ID)
			assert.NotEqual(t, uuid.Nil, tt.ledger.RecipientUserID)
			assert.NotEqual(t, uuid.Nil, tt.ledger.SourceUserID)
			assert.NotEmpty(t, tt.ledger.SourceTransactionID)
			assert.NotEmpty(t, tt.ledger.SourceTransactionType)
			assert.NotEmpty(t, tt.ledger.CommissionAsset)
			assert.NotEmpty(t, tt.ledger.Status)

			// Verify commission amount is positive
			assert.True(t, tt.ledger.CommissionAmount.GreaterThan(decimal.Zero))

			// Verify valid transaction type
			validTransactionTypes := []string{"MEME", "CONTRACT"}
			assert.Contains(t, validTransactionTypes, tt.ledger.SourceTransactionType)

			// Verify valid asset
			validAssets := []string{"SOL", "USDC"}
			assert.Contains(t, validAssets, tt.ledger.CommissionAsset)

			// Verify valid status
			validStatuses := []string{"PENDING_CLAIM", "CLAIMED"}
			assert.Contains(t, validStatuses, tt.ledger.Status)

			// If status is CLAIMED, ClaimedAt should be set
			if tt.ledger.Status == "CLAIMED" {
				assert.NotNil(t, tt.ledger.ClaimedAt)
			}
		})
	}
}

func TestCommissionLedger_Relationships(t *testing.T) {
	// Test that relationships can be set
	recipientUser := &User{ID: uuid.New()}
	sourceUser := &User{ID: uuid.New()}

	ledger := &CommissionLedger{
		RecipientUserID: recipientUser.ID,
		SourceUserID:    sourceUser.ID,
		RecipientUser:   recipientUser,
		SourceUser:      sourceUser,
	}

	assert.Equal(t, recipientUser.ID, ledger.RecipientUserID)
	assert.Equal(t, sourceUser.ID, ledger.SourceUserID)
	assert.Equal(t, recipientUser, ledger.RecipientUser)
	assert.Equal(t, sourceUser, ledger.SourceUser)
}

func TestCommissionLedger_ZeroValues(t *testing.T) {
	// Test CommissionLedger with zero values
	ledger := &CommissionLedger{}

	assert.Equal(t, uuid.Nil, ledger.ID)
	assert.Equal(t, uuid.Nil, ledger.RecipientUserID)
	assert.Equal(t, uuid.Nil, ledger.SourceUserID)
	assert.Equal(t, "", ledger.SourceTransactionID)
	assert.Equal(t, "", ledger.SourceTransactionType)
	assert.True(t, ledger.CommissionAmount.IsZero())
	assert.Equal(t, "", ledger.CommissionAsset)
	assert.Equal(t, "", ledger.Status)
	assert.Nil(t, ledger.CreatedAt)
	assert.Nil(t, ledger.UpdatedAt)
	assert.Nil(t, ledger.ClaimedAt)
	assert.Nil(t, ledger.RecipientUser)
	assert.Nil(t, ledger.SourceUser)
}

func TestCommissionLedger_StatusTransition(t *testing.T) {
	// Test status transition from PENDING_CLAIM to CLAIMED
	now := time.Now()
	
	ledger := &CommissionLedger{
		Status:    "PENDING_CLAIM",
		ClaimedAt: nil,
	}

	// Initially pending
	assert.Equal(t, "PENDING_CLAIM", ledger.Status)
	assert.Nil(t, ledger.ClaimedAt)

	// Simulate claiming
	ledger.Status = "CLAIMED"
	ledger.ClaimedAt = &now

	assert.Equal(t, "CLAIMED", ledger.Status)
	assert.NotNil(t, ledger.ClaimedAt)
	assert.Equal(t, now, *ledger.ClaimedAt)
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestCommissionLedger_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}
