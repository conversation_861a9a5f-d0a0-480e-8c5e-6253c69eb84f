package model

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAgentLevel_Struct(t *testing.T) {
	// Test that AgentLevel struct can be created and has expected fields
	level := &AgentLevel{
		ID:                      1,
		Name:                    "Lv1",
		MemeVolumeThreshold:     decimal.NewFromFloat(1000.0),
		ContractVolumeThreshold: decimal.NewFromFloat(5000.0),
		MemeFeeRate:             decimal.NewFromFloat(0.009),
		TakerFeeRate:            decimal.NewFromFloat(0.0055),
		MakerFeeRate:            decimal.NewFromFloat(0.0025),
		DirectCommissionRate:    decimal.NewFromFloat(0.30),
		IndirectCommissionRate:  decimal.NewFromFloat(0.05),
		ExtendedCommissionRate:  decimal.NewFromFloat(0.025),
		MemeFeeRebate:           decimal.NewFromFloat(0.25),
	}

	assert.Equal(t, uint(1), level.ID)
	assert.Equal(t, "Lv1", level.Name)
	assert.True(t, level.MemeVolumeThreshold.Equal(decimal.NewFromFloat(1000.0)))
	assert.True(t, level.ContractVolumeThreshold.Equal(decimal.NewFromFloat(5000.0)))
	assert.True(t, level.MemeFeeRate.Equal(decimal.NewFromFloat(0.009)))
	assert.True(t, level.TakerFeeRate.Equal(decimal.NewFromFloat(0.0055)))
	assert.True(t, level.MakerFeeRate.Equal(decimal.NewFromFloat(0.0025)))
	assert.True(t, level.DirectCommissionRate.Equal(decimal.NewFromFloat(0.30)))
	assert.True(t, level.IndirectCommissionRate.Equal(decimal.NewFromFloat(0.05)))
	assert.True(t, level.ExtendedCommissionRate.Equal(decimal.NewFromFloat(0.025)))
	assert.True(t, level.MemeFeeRebate.Equal(decimal.NewFromFloat(0.25)))
}

func TestAgentLevel_ZeroValues(t *testing.T) {
	// Test AgentLevel with zero values
	level := &AgentLevel{}

	assert.Equal(t, uint(0), level.ID)
	assert.Equal(t, "", level.Name)
	assert.True(t, level.MemeVolumeThreshold.IsZero())
	assert.True(t, level.ContractVolumeThreshold.IsZero())
	assert.True(t, level.MemeFeeRate.IsZero())
	assert.True(t, level.TakerFeeRate.IsZero())
	assert.True(t, level.MakerFeeRate.IsZero())
	assert.True(t, level.DirectCommissionRate.IsZero())
	assert.True(t, level.IndirectCommissionRate.IsZero())
	assert.True(t, level.ExtendedCommissionRate.IsZero())
	assert.True(t, level.MemeFeeRebate.IsZero())
}

func TestAgentLevel_DecimalPrecision(t *testing.T) {
	// Test decimal precision handling
	memeThreshold, _ := decimal.NewFromString("1000.50")
	contractThreshold, _ := decimal.NewFromString("5000.75")
	memeFeeRate, _ := decimal.NewFromString("0.009000")
	takerFeeRate, _ := decimal.NewFromString("0.005500")
	makerFeeRate, _ := decimal.NewFromString("0.002500")
	directRate, _ := decimal.NewFromString("0.300000")
	indirectRate, _ := decimal.NewFromString("0.050000")
	extendedRate, _ := decimal.NewFromString("0.025000")
	memeFeeRebate, _ := decimal.NewFromString("0.250000")

	level := &AgentLevel{
		ID:                      1,
		Name:                    "Lv1",
		MemeVolumeThreshold:     memeThreshold,
		ContractVolumeThreshold: contractThreshold,
		MemeFeeRate:             memeFeeRate,
		TakerFeeRate:            takerFeeRate,
		MakerFeeRate:            makerFeeRate,
		DirectCommissionRate:    directRate,
		IndirectCommissionRate:  indirectRate,
		ExtendedCommissionRate:  extendedRate,
		MemeFeeRebate:           memeFeeRebate,
	}

	// Verify precision is maintained (decimal.String() removes trailing zeros)
	assert.Equal(t, "1000.5", level.MemeVolumeThreshold.String())
	assert.Equal(t, "5000.75", level.ContractVolumeThreshold.String())
	assert.Equal(t, "0.009", level.MemeFeeRate.String())
	assert.Equal(t, "0.0055", level.TakerFeeRate.String())
	assert.Equal(t, "0.0025", level.MakerFeeRate.String())
	assert.Equal(t, "0.3", level.DirectCommissionRate.String())
	assert.Equal(t, "0.05", level.IndirectCommissionRate.String())
	assert.Equal(t, "0.025", level.ExtendedCommissionRate.String())
	assert.Equal(t, "0.25", level.MemeFeeRebate.String())
}

func TestAgentLevel_BusinessLogicValidation(t *testing.T) {
	tests := []struct {
		name        string
		level       *AgentLevel
		description string
	}{
		{
			name: "Lv1 - Entry level",
			level: &AgentLevel{
				ID:                      1,
				Name:                    "Lv1",
				MemeVolumeThreshold:     decimal.NewFromFloat(0),
				ContractVolumeThreshold: decimal.NewFromFloat(0),
				MemeFeeRate:             decimal.NewFromFloat(0.009),
				TakerFeeRate:            decimal.NewFromFloat(0.0055),
				MakerFeeRate:            decimal.NewFromFloat(0.0025),
				DirectCommissionRate:    decimal.NewFromFloat(0.30),
				IndirectCommissionRate:  decimal.NewFromFloat(0.05),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.025),
				MemeFeeRebate:           decimal.NewFromFloat(0.0),
			},
			description: "Entry level with no volume threshold",
		},
		{
			name: "Lv3 - Mid level",
			level: &AgentLevel{
				ID:                      3,
				Name:                    "Lv3",
				MemeVolumeThreshold:     decimal.NewFromFloat(10000),
				ContractVolumeThreshold: decimal.NewFromFloat(50000),
				MemeFeeRate:             decimal.NewFromFloat(0.008),
				TakerFeeRate:            decimal.NewFromFloat(0.005),
				MakerFeeRate:            decimal.NewFromFloat(0.002),
				DirectCommissionRate:    decimal.NewFromFloat(0.35),
				IndirectCommissionRate:  decimal.NewFromFloat(0.07),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.035),
				MemeFeeRebate:           decimal.NewFromFloat(0.1),
			},
			description: "Mid level with moderate thresholds and better rates",
		},
		{
			name: "Lv∞ - Infinity level",
			level: &AgentLevel{
				ID:                      6,
				Name:                    "Lv∞",
				MemeVolumeThreshold:     decimal.NewFromFloat(1000000),
				ContractVolumeThreshold: decimal.NewFromFloat(5000000),
				MemeFeeRate:             decimal.NewFromFloat(0.006),
				TakerFeeRate:            decimal.NewFromFloat(0.003),
				MakerFeeRate:            decimal.NewFromFloat(0.001),
				DirectCommissionRate:    decimal.NewFromFloat(0.50),
				IndirectCommissionRate:  decimal.NewFromFloat(0.15),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.075),
				MemeFeeRebate:           decimal.NewFromFloat(0.5),
			},
			description: "Highest level with maximum benefits",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify basic structure
			require.NotNil(t, tt.level)
			assert.NotEmpty(t, tt.level.Name)
			assert.Greater(t, tt.level.ID, uint(0))

			// Verify rates are within reasonable bounds (0-1 for percentages)
			assert.True(t, tt.level.DirectCommissionRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.DirectCommissionRate.LessThanOrEqual(decimal.NewFromFloat(1.0)))

			assert.True(t, tt.level.IndirectCommissionRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.IndirectCommissionRate.LessThanOrEqual(decimal.NewFromFloat(1.0)))

			assert.True(t, tt.level.ExtendedCommissionRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.ExtendedCommissionRate.LessThanOrEqual(decimal.NewFromFloat(1.0)))

			assert.True(t, tt.level.MemeFeeRebate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.MemeFeeRebate.LessThanOrEqual(decimal.NewFromFloat(1.0)))

			// Verify fee rates are reasonable (typically < 1%)
			assert.True(t, tt.level.MemeFeeRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.MemeFeeRate.LessThan(decimal.NewFromFloat(0.1)))

			assert.True(t, tt.level.TakerFeeRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.TakerFeeRate.LessThan(decimal.NewFromFloat(0.1)))

			assert.True(t, tt.level.MakerFeeRate.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.MakerFeeRate.LessThan(decimal.NewFromFloat(0.1)))

			// Verify volume thresholds are non-negative
			assert.True(t, tt.level.MemeVolumeThreshold.GreaterThanOrEqual(decimal.Zero))
			assert.True(t, tt.level.ContractVolumeThreshold.GreaterThanOrEqual(decimal.Zero))
		})
	}
}

func TestAgentLevel_CommissionHierarchy(t *testing.T) {
	// Test that commission rates typically follow a hierarchy
	level := &AgentLevel{
		DirectCommissionRate:   decimal.NewFromFloat(0.30),
		IndirectCommissionRate: decimal.NewFromFloat(0.05),
		ExtendedCommissionRate: decimal.NewFromFloat(0.025),
	}

	// Typically, direct > indirect > extended
	assert.True(t, level.DirectCommissionRate.GreaterThan(level.IndirectCommissionRate))
	assert.True(t, level.IndirectCommissionRate.GreaterThan(level.ExtendedCommissionRate))
}

func TestAgentLevel_FeeHierarchy(t *testing.T) {
	// Test that fee rates typically follow a hierarchy
	level := &AgentLevel{
		MemeFeeRate:  decimal.NewFromFloat(0.009),
		TakerFeeRate: decimal.NewFromFloat(0.0055),
		MakerFeeRate: decimal.NewFromFloat(0.0025),
	}

	// Typically, meme > taker > maker
	assert.True(t, level.MemeFeeRate.GreaterThan(level.TakerFeeRate))
	assert.True(t, level.TakerFeeRate.GreaterThan(level.MakerFeeRate))
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestAgentLevel_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}
