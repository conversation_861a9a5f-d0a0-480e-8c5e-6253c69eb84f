package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMemeCashback_Struct(t *testing.T) {
	// Test that MemeCashback struct can be created and has expected fields
	userID := uuid.New()
	now := time.Now()

	cashback := &MemeCashback{
		ID:                     uuid.New(),
		UserID:                 userID,
		UserAddress:            "0x1234567890abcdef",
		Status:                 "PENDING_CLAIM",
		AffiliateTransactionID: 12345,
		SolPriceUSD:            decimal.NewFromFloat(100.50),
		CashbackAmountUSD:      decimal.NewFromFloat(10.0),
		CashbackAmountSOL:      decimal.NewFromFloat(0.0995),
		CreatedAt:              &now,
		UpdatedAt:              &now,
		ClaimedAt:              nil,
	}

	assert.NotEqual(t, uuid.Nil, cashback.ID)
	assert.Equal(t, userID, cashback.UserID)
	assert.Equal(t, "0x1234567890abcdef", cashback.UserAddress)
	assert.Equal(t, "PENDING_CLAIM", cashback.Status)
	assert.Equal(t, uint(12345), cashback.AffiliateTransactionID)
	assert.True(t, cashback.SolPriceUSD.Equal(decimal.NewFromFloat(100.50)))
	assert.True(t, cashback.CashbackAmountUSD.Equal(decimal.NewFromFloat(10.0)))
	assert.True(t, cashback.CashbackAmountSOL.Equal(decimal.NewFromFloat(0.0995)))
	assert.NotNil(t, cashback.CreatedAt)
	assert.NotNil(t, cashback.UpdatedAt)
	assert.Nil(t, cashback.ClaimedAt)
}

func TestMemeCashback_TableName(t *testing.T) {
	cashback := MemeCashback{}
	assert.Equal(t, "meme_cashback", cashback.TableName())
}

func TestMemeCashback_Status(t *testing.T) {
	validStatuses := []string{"PENDING_CLAIM", "CLAIMED"}

	for _, status := range validStatuses {
		t.Run("valid_status_"+status, func(t *testing.T) {
			cashback := &MemeCashback{
				Status: status,
			}

			assert.Contains(t, validStatuses, cashback.Status)
		})
	}
}

func TestMemeCashback_DecimalPrecision(t *testing.T) {
	tests := []struct {
		name     string
		field    string
		amount   string
		expected string
	}{
		{
			name:     "SOL price with high precision",
			field:    "SolPriceUSD",
			amount:   "100.123456789012345678",
			expected: "100.123456789012345678",
		},
		{
			name:     "USD cashback amount",
			field:    "CashbackAmountUSD",
			amount:   "10.50",
			expected: "10.5",
		},
		{
			name:     "SOL cashback amount with high precision",
			field:    "CashbackAmountSOL",
			amount:   "0.104477611940298507",
			expected: "0.104477611940298507",
		},
		{
			name:     "zero amount",
			field:    "CashbackAmountUSD",
			amount:   "0",
			expected: "0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			amount, err := decimal.NewFromString(tt.amount)
			require.NoError(t, err)

			cashback := &MemeCashback{}
			switch tt.field {
			case "SolPriceUSD":
				cashback.SolPriceUSD = amount
				assert.Equal(t, tt.expected, cashback.SolPriceUSD.String())
			case "CashbackAmountUSD":
				cashback.CashbackAmountUSD = amount
				assert.Equal(t, tt.expected, cashback.CashbackAmountUSD.String())
			case "CashbackAmountSOL":
				cashback.CashbackAmountSOL = amount
				assert.Equal(t, tt.expected, cashback.CashbackAmountSOL.String())
			}
		})
	}
}

func TestMemeCashback_BusinessLogicValidation(t *testing.T) {
	tests := []struct {
		name        string
		cashback    *MemeCashback
		description string
	}{
		{
			name: "pending cashback",
			cashback: &MemeCashback{
				ID:                     uuid.New(),
				UserID:                 uuid.New(),
				UserAddress:            "0x1234567890abcdef",
				Status:                 "PENDING_CLAIM",
				AffiliateTransactionID: 12345,
				SolPriceUSD:            decimal.NewFromFloat(100.0),
				CashbackAmountUSD:      decimal.NewFromFloat(10.0),
				CashbackAmountSOL:      decimal.NewFromFloat(0.1),
			},
			description: "Pending cashback ready to be claimed",
		},
		{
			name: "claimed cashback",
			cashback: &MemeCashback{
				ID:                     uuid.New(),
				UserID:                 uuid.New(),
				UserAddress:            "0xabcdef1234567890",
				Status:                 "CLAIMED",
				AffiliateTransactionID: 67890,
				SolPriceUSD:            decimal.NewFromFloat(95.50),
				CashbackAmountUSD:      decimal.NewFromFloat(5.0),
				CashbackAmountSOL:      decimal.NewFromFloat(0.0523),
				ClaimedAt:              &time.Time{},
			},
			description: "Cashback that has been claimed",
		},
		{
			name: "high precision amounts",
			cashback: func() *MemeCashback {
				solPrice, _ := decimal.NewFromString("123.456789012345678901")
				usdAmount, _ := decimal.NewFromString("1.234567890123456789")
				solAmount, _ := decimal.NewFromString("0.010000000000000001")
				return &MemeCashback{
					ID:                     uuid.New(),
					UserID:                 uuid.New(),
					UserAddress:            "0xfedcba0987654321",
					Status:                 "PENDING_CLAIM",
					AffiliateTransactionID: 11111,
					SolPriceUSD:            solPrice,
					CashbackAmountUSD:      usdAmount,
					CashbackAmountSOL:      solAmount,
				}
			}(),
			description: "Cashback with high precision decimal amounts",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Verify basic structure
			require.NotNil(t, tt.cashback)
			assert.NotEqual(t, uuid.Nil, tt.cashback.ID)
			assert.NotEqual(t, uuid.Nil, tt.cashback.UserID)
			assert.NotEmpty(t, tt.cashback.UserAddress)
			assert.NotEmpty(t, tt.cashback.Status)
			assert.Greater(t, tt.cashback.AffiliateTransactionID, uint(0))

			// Verify amounts are positive
			assert.True(t, tt.cashback.SolPriceUSD.GreaterThan(decimal.Zero))
			assert.True(t, tt.cashback.CashbackAmountUSD.GreaterThan(decimal.Zero))
			assert.True(t, tt.cashback.CashbackAmountSOL.GreaterThan(decimal.Zero))

			// Verify valid status
			validStatuses := []string{"PENDING_CLAIM", "CLAIMED"}
			assert.Contains(t, validStatuses, tt.cashback.Status)

			// If status is CLAIMED, ClaimedAt should be set
			if tt.cashback.Status == "CLAIMED" {
				assert.NotNil(t, tt.cashback.ClaimedAt)
			}

			// Verify conversion logic: CashbackAmountSOL ≈ CashbackAmountUSD / SolPriceUSD
			expectedSOL := tt.cashback.CashbackAmountUSD.Div(tt.cashback.SolPriceUSD)
			// Allow for reasonable rounding differences (especially for test data)
			diff := tt.cashback.CashbackAmountSOL.Sub(expectedSOL).Abs()
			tolerance := decimal.NewFromFloat(0.1) // More generous tolerance for test data
			assert.True(t, diff.LessThan(tolerance),
				"SOL amount should be approximately USD amount divided by SOL price. Expected: %s, Got: %s, Diff: %s",
				expectedSOL.String(), tt.cashback.CashbackAmountSOL.String(), diff.String())
		})
	}
}

func TestMemeCashback_Relationships(t *testing.T) {
	// Test that relationships can be set
	user := &User{ID: uuid.New()}

	cashback := &MemeCashback{
		UserID: user.ID,
		User:   user,
	}

	assert.Equal(t, user.ID, cashback.UserID)
	assert.Equal(t, user, cashback.User)
}

func TestMemeCashback_ZeroValues(t *testing.T) {
	// Test MemeCashback with zero values
	cashback := &MemeCashback{}

	assert.Equal(t, uuid.Nil, cashback.ID)
	assert.Equal(t, uuid.Nil, cashback.UserID)
	assert.Equal(t, "", cashback.UserAddress)
	assert.Equal(t, "", cashback.Status)
	assert.Equal(t, uint(0), cashback.AffiliateTransactionID)
	assert.True(t, cashback.SolPriceUSD.IsZero())
	assert.True(t, cashback.CashbackAmountUSD.IsZero())
	assert.True(t, cashback.CashbackAmountSOL.IsZero())
	assert.Nil(t, cashback.CreatedAt)
	assert.Nil(t, cashback.UpdatedAt)
	assert.Nil(t, cashback.ClaimedAt)
	assert.Nil(t, cashback.User)
}

func TestMemeCashback_StatusTransition(t *testing.T) {
	// Test status transition from PENDING_CLAIM to CLAIMED
	now := time.Now()

	cashback := &MemeCashback{
		Status:    "PENDING_CLAIM",
		ClaimedAt: nil,
	}

	// Initially pending
	assert.Equal(t, "PENDING_CLAIM", cashback.Status)
	assert.Nil(t, cashback.ClaimedAt)

	// Simulate claiming
	cashback.Status = "CLAIMED"
	cashback.ClaimedAt = &now

	assert.Equal(t, "CLAIMED", cashback.Status)
	assert.NotNil(t, cashback.ClaimedAt)
	assert.Equal(t, now, *cashback.ClaimedAt)
}

func TestMemeCashback_ConversionLogic(t *testing.T) {
	// Test the conversion logic between USD and SOL amounts
	tests := []struct {
		name        string
		solPrice    float64
		usdAmount   float64
		expectedSOL float64
		tolerance   float64
	}{
		{
			name:        "standard conversion",
			solPrice:    100.0,
			usdAmount:   10.0,
			expectedSOL: 0.1,
			tolerance:   0.000001,
		},
		{
			name:        "high SOL price",
			solPrice:    200.0,
			usdAmount:   20.0,
			expectedSOL: 0.1,
			tolerance:   0.000001,
		},
		{
			name:        "low SOL price",
			solPrice:    50.0,
			usdAmount:   5.0,
			expectedSOL: 0.1,
			tolerance:   0.000001,
		},
		{
			name:        "fractional amounts",
			solPrice:    123.456,
			usdAmount:   1.23456,
			expectedSOL: 0.01,
			tolerance:   0.000001,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			solPrice := decimal.NewFromFloat(tt.solPrice)
			usdAmount := decimal.NewFromFloat(tt.usdAmount)
			expectedSOL := decimal.NewFromFloat(tt.expectedSOL)

			// Calculate SOL amount
			calculatedSOL := usdAmount.Div(solPrice)

			// Verify conversion
			diff := calculatedSOL.Sub(expectedSOL).Abs()
			tolerance := decimal.NewFromFloat(tt.tolerance)

			assert.True(t, diff.LessThan(tolerance),
				"Conversion should be accurate. Expected: %s, Got: %s, Diff: %s",
				expectedSOL.String(), calculatedSOL.String(), diff.String())
		})
	}
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestMemeCashback_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}
