//go:build integration
// +build integration

package integration

import (
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestCommissionWorkflow tests the complete commission calculation and distribution workflow
func TestCommissionWorkflow(t *testing.T) {
	helper, fixtures := SetupIntegrationTest(t)
	defer TeardownIntegrationTest()

	t.Run("meme transaction commission workflow", func(t *testing.T) {
		ctx := helper.CreateTestContext()

		// Setup: Create a referral chain
		// Level 1: Referrer (gets direct commission)
		referrerID := helper.GenerateTestUUID()
		_ = &model.User{
			ID:           referrerID,
			AgentLevelID: 2, // Level 2 agent
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		// Level 2: Trader (generates transaction)
		traderID := helper.GenerateTestUUID()
		_ = &model.User{
			ID:           traderID,
			AgentLevelID: 1, // Level 1 agent
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		// Create referral relationship
		referral := &model.Referral{
			UserID:     traderID,
			ReferrerID: &referrerID,
			Depth:      1,
			CreatedAt:  time.Now(),
		}

		// Step 1: Create a meme transaction (using AffiliateTransaction)
		transactionAmount := decimal.NewFromFloat(1000.0) // $1000 transaction
		feeRate := decimal.NewFromFloat(0.009)            // 0.9% fee
		transactionFee := transactionAmount.Mul(feeRate)  // $9 fee

		transaction := &model.AffiliateTransaction{
			ID:              1,
			OrderID:         helper.GenerateTestUUID(),
			UserID:          traderID,
			TxHash:          "0x" + helper.GenerateTestUUID().String(),
			VolumeUSD:       transactionAmount,
			TotalFee:        transactionFee,
			Status:          model.StatusCompleted,
			TransactionType: model.Buy, // Buy transaction
			Type:            model.Market,
			ChainID:         "solana",
			BaseAddress:     "0x123",
			BaseSymbol:      "MEME",
			QuoteAddress:    "0x456",
			QuoteSymbol:     "SOL",
			UserAddress:     "0x789",
			BaseAmount:      transactionAmount,
			QuoteAmount:     transactionAmount,
			Slippage:        decimal.NewFromFloat(0.01),
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		// Step 2: Calculate commission rates based on agent level
		level2Rates := &model.AgentLevel{
			ID:                     2,
			Name:                   "Lv2",
			DirectCommissionRate:   decimal.NewFromFloat(0.35),  // 35%
			IndirectCommissionRate: decimal.NewFromFloat(0.07),  // 7%
			ExtendedCommissionRate: decimal.NewFromFloat(0.035), // 3.5%
			MemeFeeRebate:          decimal.NewFromFloat(0.1),   // 10%
		}

		// Step 3: Calculate direct commission for referrer
		directCommissionAmount := transactionFee.Mul(level2Rates.DirectCommissionRate)

		directCommission := &model.CommissionLedger{
			ID:                    helper.GenerateTestUUID(),
			RecipientUserID:       referrerID,
			SourceUserID:          traderID,
			SourceTransactionID:   transaction.TxHash,
			SourceTransactionType: "MEME",
			CommissionAmount:      directCommissionAmount,
			CommissionAsset:       "SOL",
			Status:                "PENDING_CLAIM",
			CreatedAt:             &[]time.Time{time.Now()}[0],
			UpdatedAt:             &[]time.Time{time.Now()}[0],
		}

		// Step 4: Calculate cashback for trader
		cashbackAmount := transactionFee.Mul(level2Rates.MemeFeeRebate)

		cashback := &model.MemeCashback{
			ID:                     helper.GenerateTestUUID(),
			UserID:                 traderID,
			UserAddress:            "0x1234567890abcdef",
			Status:                 "PENDING_CLAIM",
			AffiliateTransactionID: 12345,
			SolPriceUSD:            decimal.NewFromFloat(100.0),
			CashbackAmountUSD:      cashbackAmount,
			CashbackAmountSOL:      cashbackAmount.Div(decimal.NewFromFloat(100.0)),
			CreatedAt:              &[]time.Time{time.Now()}[0],
			UpdatedAt:              &[]time.Time{time.Now()}[0],
		}

		// Verify calculations
		expectedDirectCommission := decimal.NewFromFloat(3.15) // $9 * 35% = $3.15
		expectedCashback := decimal.NewFromFloat(0.9)          // $9 * 10% = $0.9

		helper.AssertTrue(directCommissionAmount.Equal(expectedDirectCommission))
		helper.AssertTrue(cashbackAmount.Equal(expectedCashback))

		// Verify commission structure
		helper.AssertEqual(referrerID, directCommission.RecipientUserID)
		helper.AssertEqual(traderID, directCommission.SourceUserID)
		helper.AssertEqual("MEME", directCommission.SourceTransactionType)
		helper.AssertEqual("PENDING_CLAIM", directCommission.Status)

		// Verify cashback structure
		helper.AssertEqual(traderID, cashback.UserID)
		helper.AssertEqual("PENDING_CLAIM", cashback.Status)
		helper.AssertTrue(cashback.CashbackAmountUSD.Equal(expectedCashback))

		// Verify referral relationship
		helper.AssertEqual(traderID, referral.UserID)
		helper.AssertEqual(referrerID, *referral.ReferrerID)
		helper.AssertEqual(1, referral.Depth)

		t.Logf("Transaction: $%s, Fee: $%s", transactionAmount.String(), transactionFee.String())
		t.Logf("Direct Commission: $%s for referrer %s", directCommissionAmount.String(), referrerID)
		t.Logf("Cashback: $%s for trader %s", cashbackAmount.String(), traderID)

		_ = ctx
	})

	t.Run("contract transaction commission workflow", func(t *testing.T) {
		// Setup: Create a 3-level referral chain for contract trading
		// Level 1: Top referrer (gets extended commission)
		topReferrerID := helper.GenerateTestUUID()

		// Level 2: Mid referrer (gets indirect commission)
		midReferrerID := helper.GenerateTestUUID()

		// Level 3: Direct referrer (gets direct commission)
		directReferrerID := helper.GenerateTestUUID()

		// Level 4: Trader (generates transaction)
		traderID := helper.GenerateTestUUID()

		// Create referral chain
		_ = &model.User{
			ID:           traderID,
			AgentLevelID: 1,
		}

		_ = &model.User{
			ID:           directReferrerID,
			AgentLevelID: 2,
		}

		_ = &model.User{
			ID:           midReferrerID,
			AgentLevelID: 3,
		}

		_ = &model.User{
			ID:           topReferrerID,
			AgentLevelID: 4,
		}

		// Create referral relationships
		referrals := []*model.Referral{
			{UserID: traderID, ReferrerID: &directReferrerID, Depth: 1},
			{UserID: directReferrerID, ReferrerID: &midReferrerID, Depth: 1},
			{UserID: midReferrerID, ReferrerID: &topReferrerID, Depth: 1},
		}

		// Create contract transaction
		transactionAmount := decimal.NewFromFloat(10000.0) // $10,000 contract trade
		feeRate := decimal.NewFromFloat(0.0055)            // 0.55% taker fee
		transactionFee := transactionAmount.Mul(feeRate)   // $55 fee

		cloidValue := "contract_" + helper.GenerateTestUUID().String()
		contractTx := &model.HyperLiquidTransaction{
			Cloid:    cloidValue,
			UserID:   &traderID,
			Size:     &transactionAmount,
			BuildFee: &transactionFee,
			Status:   helper.StringToPointer("filled"),
		}

		// Calculate multi-level commissions
		level4Rates := &model.AgentLevel{
			ID:                     4,
			DirectCommissionRate:   decimal.NewFromFloat(0.40), // 40%
			IndirectCommissionRate: decimal.NewFromFloat(0.10), // 10%
			ExtendedCommissionRate: decimal.NewFromFloat(0.05), // 5%
		}

		// Direct commission (Level 3 → Level 4)
		directCommission := transactionFee.Mul(level4Rates.DirectCommissionRate)

		// Indirect commission (Level 2 → Level 3)
		indirectCommission := transactionFee.Mul(level4Rates.IndirectCommissionRate)

		// Extended commission (Level 1 → Level 2)
		extendedCommission := transactionFee.Mul(level4Rates.ExtendedCommissionRate)

		// Create commission ledger entries
		commissions := []*model.CommissionLedger{
			{
				ID:                    helper.GenerateTestUUID(),
				RecipientUserID:       directReferrerID,
				SourceUserID:          traderID,
				SourceTransactionID:   contractTx.Cloid,
				SourceTransactionType: "CONTRACT",
				CommissionAmount:      directCommission,
				CommissionAsset:       "USDC",
				Status:                "PENDING_CLAIM",
			},
			{
				ID:                    helper.GenerateTestUUID(),
				RecipientUserID:       midReferrerID,
				SourceUserID:          traderID,
				SourceTransactionID:   contractTx.Cloid,
				SourceTransactionType: "CONTRACT",
				CommissionAmount:      indirectCommission,
				CommissionAsset:       "USDC",
				Status:                "PENDING_CLAIM",
			},
			{
				ID:                    helper.GenerateTestUUID(),
				RecipientUserID:       topReferrerID,
				SourceUserID:          traderID,
				SourceTransactionID:   contractTx.Cloid,
				SourceTransactionType: "CONTRACT",
				CommissionAmount:      extendedCommission,
				CommissionAsset:       "USDC",
				Status:                "PENDING_CLAIM",
			},
		}

		// Verify calculations
		expectedDirect := decimal.NewFromFloat(22.0)   // $55 * 40% = $22
		expectedIndirect := decimal.NewFromFloat(5.5)  // $55 * 10% = $5.5
		expectedExtended := decimal.NewFromFloat(2.75) // $55 * 5% = $2.75

		helper.AssertTrue(directCommission.Equal(expectedDirect))
		helper.AssertTrue(indirectCommission.Equal(expectedIndirect))
		helper.AssertTrue(extendedCommission.Equal(expectedExtended))

		// Verify commission distribution
		helper.AssertLen(commissions, 3)

		for i, commission := range commissions {
			helper.AssertEqual("CONTRACT", commission.SourceTransactionType)
			helper.AssertEqual("USDC", commission.CommissionAsset)
			helper.AssertEqual("PENDING_CLAIM", commission.Status)
			helper.AssertEqual(traderID, commission.SourceUserID)

			t.Logf("Commission %d: $%s for user %s",
				i+1, commission.CommissionAmount.String(), commission.RecipientUserID)
		}

		totalCommissions := directCommission.Add(indirectCommission).Add(extendedCommission)
		t.Logf("Total commissions distributed: $%s (%.2f%% of fee)",
			totalCommissions.String(),
			totalCommissions.Div(transactionFee).Mul(decimal.NewFromFloat(100)).InexactFloat64())

		// Verify referral chain integrity
		helper.AssertEqual(directReferrerID, *referrals[0].ReferrerID)
		helper.AssertEqual(midReferrerID, *referrals[1].ReferrerID)
		helper.AssertEqual(topReferrerID, *referrals[2].ReferrerID)
		helper.AssertEqual(traderID, referrals[0].UserID)
		helper.AssertEqual(directReferrerID, referrals[1].UserID)
		helper.AssertEqual(midReferrerID, referrals[2].UserID)
	})

	t.Run("commission claiming workflow", func(t *testing.T) {
		userID := helper.GenerateTestUUID()

		// Create pending commission
		commission := &model.CommissionLedger{
			ID:                    helper.GenerateTestUUID(),
			RecipientUserID:       userID,
			SourceUserID:          helper.GenerateTestUUID(),
			SourceTransactionID:   "tx_123",
			SourceTransactionType: "MEME",
			CommissionAmount:      decimal.NewFromFloat(5.0),
			CommissionAsset:       "SOL",
			Status:                "PENDING_CLAIM",
			CreatedAt:             &[]time.Time{time.Now()}[0],
			UpdatedAt:             &[]time.Time{time.Now()}[0],
		}

		// Simulate claiming process
		helper.AssertEqual("PENDING_CLAIM", commission.Status)
		helper.AssertNil(commission.ClaimedAt)

		// Claim commission
		claimTime := time.Now()
		commission.Status = "CLAIMED"
		commission.ClaimedAt = &claimTime

		// Verify claim
		helper.AssertEqual("CLAIMED", commission.Status)
		helper.AssertNotNil(commission.ClaimedAt)
		helper.AssertTrue(commission.ClaimedAt.After(commission.CreatedAt.Add(-time.Second)))

		t.Logf("Commission claimed: $%s %s at %s",
			commission.CommissionAmount.String(),
			commission.CommissionAsset,
			commission.ClaimedAt.Format(time.RFC3339))
	})

	_ = fixtures
}
