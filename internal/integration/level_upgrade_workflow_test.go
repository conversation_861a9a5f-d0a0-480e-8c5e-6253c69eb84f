//go:build integration
// +build integration

package integration

import (
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestLevelUpgradeWorkflow tests the complete agent level upgrade workflow
func TestLevelUpgradeWorkflow(t *testing.T) {
	helper, fixtures := SetupIntegrationTest(t)
	defer TeardownIntegrationTest()

	t.Run("volume-based level upgrade workflow", func(t *testing.T) {
		// Setup: Create agent levels
		levels := []model.AgentLevel{
			{
				ID:                      1,
				Name:                    "Lv1",
				MemeVolumeThreshold:     decimal.NewFromFloat(0),
				ContractVolumeThreshold: decimal.NewFromFloat(0),
				DirectCommissionRate:    decimal.NewFromFloat(0.30),
				IndirectCommissionRate:  decimal.NewFromFloat(0.05),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.025),
				MemeFeeRebate:           decimal.NewFromFloat(0.0),
			},
			{
				ID:                      2,
				Name:                    "Lv2",
				MemeVolumeThreshold:     decimal.NewFromFloat(1000),
				ContractVolumeThreshold: decimal.NewFromFloat(5000),
				DirectCommissionRate:    decimal.NewFromFloat(0.35),
				IndirectCommissionRate:  decimal.NewFromFloat(0.07),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.035),
				MemeFeeRebate:           decimal.NewFromFloat(0.1),
			},
			{
				ID:                      3,
				Name:                    "Lv3",
				MemeVolumeThreshold:     decimal.NewFromFloat(10000),
				ContractVolumeThreshold: decimal.NewFromFloat(50000),
				DirectCommissionRate:    decimal.NewFromFloat(0.40),
				IndirectCommissionRate:  decimal.NewFromFloat(0.10),
				ExtendedCommissionRate:  decimal.NewFromFloat(0.05),
				MemeFeeRebate:           decimal.NewFromFloat(0.2),
			},
		}

		// Create user starting at Level 1
		userID := helper.GenerateTestUUID()
		user := &model.User{
			ID:           userID,
			AgentLevelID: 1,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		// Step 1: User starts at Level 1
		helper.AssertEqual(uint(1), user.AgentLevelID)

		// Step 2: Generate meme trading volume
		memeVolume := decimal.NewFromFloat(1500) // $1,500 in meme trading

		// Check if user qualifies for Level 2 (needs $1,000 meme OR $5,000 contract)
		level2 := levels[1]
		qualifiesForLevel2 := memeVolume.GreaterThanOrEqual(level2.MemeVolumeThreshold)
		helper.AssertTrue(qualifiesForLevel2)

		// Upgrade to Level 2
		if qualifiesForLevel2 {
			user.AgentLevelID = 2
			user.UpdatedAt = time.Now()
		}

		helper.AssertEqual(uint(2), user.AgentLevelID)

		// Step 3: Generate more volume for Level 3
		additionalMemeVolume := decimal.NewFromFloat(8500)      // Additional $8,500
		totalMemeVolume := memeVolume.Add(additionalMemeVolume) // Total: $10,000

		// Check if user qualifies for Level 3 (needs $10,000 meme OR $50,000 contract)
		level3 := levels[2]
		qualifiesForLevel3 := totalMemeVolume.GreaterThanOrEqual(level3.MemeVolumeThreshold)
		helper.AssertTrue(qualifiesForLevel3)

		// Upgrade to Level 3
		if qualifiesForLevel3 {
			user.AgentLevelID = 3
			user.UpdatedAt = time.Now()
		}

		helper.AssertEqual(uint(3), user.AgentLevelID)

		// Step 4: Verify commission rate improvements
		level1Rates := levels[0]
		level3Rates := levels[2]

		// Commission rates should improve with level
		helper.AssertTrue(level3Rates.DirectCommissionRate.GreaterThan(level1Rates.DirectCommissionRate))
		helper.AssertTrue(level3Rates.IndirectCommissionRate.GreaterThan(level1Rates.IndirectCommissionRate))
		helper.AssertTrue(level3Rates.ExtendedCommissionRate.GreaterThan(level1Rates.ExtendedCommissionRate))
		helper.AssertTrue(level3Rates.MemeFeeRebate.GreaterThan(level1Rates.MemeFeeRebate))

		t.Logf("User %s upgraded from Level 1 to Level 3", userID)
		t.Logf("Total meme volume: $%s", totalMemeVolume.String())
		t.Logf("Level 3 benefits:")
		t.Logf("- Direct commission: %s%%", level3Rates.DirectCommissionRate.Mul(decimal.NewFromFloat(100)).String())
		t.Logf("- Indirect commission: %s%%", level3Rates.IndirectCommissionRate.Mul(decimal.NewFromFloat(100)).String())
		t.Logf("- Extended commission: %s%%", level3Rates.ExtendedCommissionRate.Mul(decimal.NewFromFloat(100)).String())
		t.Logf("- Meme fee rebate: %s%%", level3Rates.MemeFeeRebate.Mul(decimal.NewFromFloat(100)).String())
	})

	t.Run("contract volume upgrade path", func(t *testing.T) {
		// Test upgrade through contract trading volume
		userID := helper.GenerateTestUUID()
		user := &model.User{
			ID:           userID,
			AgentLevelID: 1,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}

		// Generate contract trading volume
		contractVolume := decimal.NewFromFloat(60000) // $60,000 in contract trading

		// Should qualify for Level 3 through contract volume
		level3ContractThreshold := decimal.NewFromFloat(50000)
		qualifiesForLevel3 := contractVolume.GreaterThanOrEqual(level3ContractThreshold)
		helper.AssertTrue(qualifiesForLevel3)

		// Upgrade directly to Level 3
		if qualifiesForLevel3 {
			user.AgentLevelID = 3
			user.UpdatedAt = time.Now()
		}

		helper.AssertEqual(uint(3), user.AgentLevelID)

		t.Logf("User %s upgraded to Level 3 via contract volume: $%s", userID, contractVolume.String())
	})

	t.Run("level downgrade grace period workflow", func(t *testing.T) {
		// Test the grace period mechanism for level downgrades
		userID := helper.GenerateTestUUID()

		// User starts at Level 3
		user := &model.User{
			ID:           userID,
			AgentLevelID: 3,
			CreatedAt:    time.Now().AddDate(0, 0, -90), // 90 days ago
			UpdatedAt:    time.Now(),
		}

		// Simulate volume drop below Level 3 threshold
		currentVolume := decimal.NewFromFloat(5000) // Below $10,000 meme threshold
		level3Threshold := decimal.NewFromFloat(10000)

		helper.AssertTrue(currentVolume.LessThan(level3Threshold))

		// Grace period phases
		gracePeriodStart := time.Now().AddDate(0, 0, -45) // Started 45 days ago

		// Phase 1: Observation period (0-30 days) - no action
		observationEnd := gracePeriodStart.AddDate(0, 0, 30)

		// Phase 2: Warning period (31-60 days) - send warnings
		warningEnd := gracePeriodStart.AddDate(0, 0, 60)

		// Phase 3: Downgrade period (61+ days) - perform downgrade
		now := time.Now()

		daysSinceGraceStart := int(now.Sub(gracePeriodStart).Hours() / 24)

		var phase string
		var shouldDowngrade bool

		if daysSinceGraceStart <= 30 {
			phase = "observation"
			shouldDowngrade = false
		} else if daysSinceGraceStart <= 60 {
			phase = "warning"
			shouldDowngrade = false
		} else {
			phase = "downgrade"
			shouldDowngrade = true
		}

		helper.AssertEqual("downgrade", phase) // Should be in downgrade phase
		helper.AssertTrue(shouldDowngrade)

		// Perform downgrade
		if shouldDowngrade {
			// Find appropriate level for current volume
			newLevel := uint(2) // Downgrade to Level 2
			if currentVolume.LessThan(decimal.NewFromFloat(1000)) {
				newLevel = 1 // Downgrade to Level 1
			}

			user.AgentLevelID = newLevel
			user.UpdatedAt = now
		}

		helper.AssertEqual(uint(2), user.AgentLevelID) // Should be downgraded to Level 2

		t.Logf("User %s downgraded from Level 3 to Level %d after %d days grace period",
			userID, user.AgentLevelID, daysSinceGraceStart)

		_ = observationEnd
		_ = warningEnd
	})

	t.Run("level upgrade benefits calculation", func(t *testing.T) {
		// Test the financial impact of level upgrades
		transactionFee := decimal.NewFromFloat(100) // $100 fee

		// Level 1 benefits
		level1DirectRate := decimal.NewFromFloat(0.30)
		level1Commission := transactionFee.Mul(level1DirectRate) // $30

		// Level 3 benefits
		level3DirectRate := decimal.NewFromFloat(0.40)
		level3Commission := transactionFee.Mul(level3DirectRate) // $40

		// Calculate improvement
		improvement := level3Commission.Sub(level1Commission) // $10 more
		improvementPercent := improvement.Div(level1Commission).Mul(decimal.NewFromFloat(100))

		helper.AssertTrue(level3Commission.GreaterThan(level1Commission))
		helper.AssertTrue(improvement.Equal(decimal.NewFromFloat(10)))
		helper.AssertTrue(improvementPercent.Equal(decimal.NewFromFloat(33.33).Round(2)))

		t.Logf("Commission improvement from Level 1 to Level 3:")
		t.Logf("- Level 1: $%s", level1Commission.String())
		t.Logf("- Level 3: $%s", level3Commission.String())
		t.Logf("- Improvement: $%s (%s%%)", improvement.String(), improvementPercent.String())
	})

	t.Run("mixed volume qualification", func(t *testing.T) {
		// Test qualification with both meme and contract volume
		userID := helper.GenerateTestUUID()
		user := &model.User{
			ID:           userID,
			AgentLevelID: 1,
		}

		// Partial volumes that individually don't qualify but together do
		memeVolume := decimal.NewFromFloat(6000)      // $6,000 meme (below $10,000 threshold)
		contractVolume := decimal.NewFromFloat(30000) // $30,000 contract (below $50,000 threshold)

		// Neither volume alone qualifies for Level 3
		level3MemeThreshold := decimal.NewFromFloat(10000)
		level3ContractThreshold := decimal.NewFromFloat(50000)

		helper.AssertTrue(memeVolume.LessThan(level3MemeThreshold))
		helper.AssertTrue(contractVolume.LessThan(level3ContractThreshold))

		// But user should qualify for Level 2 with meme volume
		level2MemeThreshold := decimal.NewFromFloat(1000)
		level2ContractThreshold := decimal.NewFromFloat(5000)

		qualifiesLevel2Meme := memeVolume.GreaterThanOrEqual(level2MemeThreshold)
		qualifiesLevel2Contract := contractVolume.GreaterThanOrEqual(level2ContractThreshold)

		helper.AssertTrue(qualifiesLevel2Meme)
		helper.AssertTrue(qualifiesLevel2Contract)

		// Upgrade to Level 2
		user.AgentLevelID = 2

		helper.AssertEqual(uint(2), user.AgentLevelID)

		t.Logf("User %s qualified for Level 2 with mixed volumes:", userID)
		t.Logf("- Meme volume: $%s", memeVolume.String())
		t.Logf("- Contract volume: $%s", contractVolume.String())
	})

	_ = fixtures
}
