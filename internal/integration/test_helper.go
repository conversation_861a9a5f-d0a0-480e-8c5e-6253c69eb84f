//go:build integration
// +build integration

package integration

import (
	"context"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// TestHelper provides utilities for integration tests
type TestHelper struct {
	t *testing.T
}

// NewTestHelper creates a new test helper
func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

// GenerateTestUUID generates a new UUID for testing
func (h *TestHelper) GenerateTestUUID() uuid.UUID {
	return uuid.New()
}

// GenerateTestEmail generates a test email address
func (h *TestHelper) GenerateTestEmail() string {
	return fmt.Sprintf("<EMAIL>", rand.Int63())
}

// GenerateTestInvitationCode generates a test invitation code
func (h *TestHelper) GenerateTestInvitationCode() string {
	codes := []string{
		"TEST123", "INVITE456", "AGENT789", "REFER001", "CODE999",
		"ALPHA123", "BETA456", "GAMMA789", "DELTA001", "OMEGA999",
	}
	return codes[rand.Intn(len(codes))] + fmt.Sprintf("%d", rand.Intn(1000))
}

// CreateTestContext creates a test context
func (h *TestHelper) CreateTestContext() context.Context {
	return context.Background()
}

// StringToPointer converts string to pointer
func (h *TestHelper) StringToPointer(s string) *string {
	return &s
}

// GetTestDecimal creates a decimal from string for testing
func (h *TestHelper) GetTestDecimal(s string) decimal.Decimal {
	d, _ := decimal.NewFromString(s)
	return d
}

// AssertEqual asserts that two values are equal
func (h *TestHelper) AssertEqual(expected, actual interface{}) {
	if h.t != nil {
		assert.Equal(h.t, expected, actual)
	}
}

// AssertNotEqual asserts that two values are not equal
func (h *TestHelper) AssertNotEqual(expected, actual interface{}) {
	if h.t != nil {
		assert.NotEqual(h.t, expected, actual)
	}
}

// AssertTrue asserts that a condition is true
func (h *TestHelper) AssertTrue(condition bool) {
	if h.t != nil {
		assert.True(h.t, condition)
	}
}

// AssertFalse asserts that a condition is false
func (h *TestHelper) AssertFalse(condition bool) {
	if h.t != nil {
		assert.False(h.t, condition)
	}
}

// AssertNotNil asserts that a value is not nil
func (h *TestHelper) AssertNotNil(value interface{}) {
	if h.t != nil {
		assert.NotNil(h.t, value)
	}
}

// AssertNil asserts that a value is nil
func (h *TestHelper) AssertNil(value interface{}) {
	if h.t != nil {
		assert.Nil(h.t, value)
	}
}

// AssertNotEmpty asserts that a string is not empty
func (h *TestHelper) AssertNotEmpty(value string) {
	if h.t != nil {
		assert.NotEmpty(h.t, value)
	}
}

// AssertLen asserts that a collection has the expected length
func (h *TestHelper) AssertLen(collection interface{}, expectedLen int) {
	if h.t != nil {
		assert.Len(h.t, collection, expectedLen)
	}
}

// AssertGreaterThan asserts that a decimal is greater than another
func (h *TestHelper) AssertGreaterThan(actual, expected decimal.Decimal) {
	if h.t != nil {
		assert.True(h.t, actual.GreaterThan(expected), 
			fmt.Sprintf("Expected %s to be greater than %s", actual.String(), expected.String()))
	}
}

// AssertLessThan asserts that a decimal is less than another
func (h *TestHelper) AssertLessThan(actual, expected decimal.Decimal) {
	if h.t != nil {
		assert.True(h.t, actual.LessThan(expected),
			fmt.Sprintf("Expected %s to be less than %s", actual.String(), expected.String()))
	}
}

// AssertDecimalEqual asserts that two decimals are equal
func (h *TestHelper) AssertDecimalEqual(expected, actual decimal.Decimal) {
	if h.t != nil {
		assert.True(h.t, expected.Equal(actual),
			fmt.Sprintf("Expected %s to equal %s", actual.String(), expected.String()))
	}
}

// TestFixtures provides test data fixtures
type TestFixtures struct{}

// NewTestFixtures creates new test fixtures
func NewTestFixtures() *TestFixtures {
	return &TestFixtures{}
}

// GetTestDecimal creates a decimal from string
func (f *TestFixtures) GetTestDecimal(s string) decimal.Decimal {
	d, _ := decimal.NewFromString(s)
	return d
}

// GetTestAgentLevels returns test agent levels
func (f *TestFixtures) GetTestAgentLevels() []TestAgentLevel {
	return []TestAgentLevel{
		{
			ID:                      1,
			Name:                    "Lv1",
			MemeVolumeThreshold:     decimal.NewFromFloat(0),
			ContractVolumeThreshold: decimal.NewFromFloat(0),
			DirectCommissionRate:    decimal.NewFromFloat(0.30),
			IndirectCommissionRate:  decimal.NewFromFloat(0.05),
			ExtendedCommissionRate:  decimal.NewFromFloat(0.025),
			MemeFeeRebate:           decimal.NewFromFloat(0.0),
		},
		{
			ID:                      2,
			Name:                    "Lv2",
			MemeVolumeThreshold:     decimal.NewFromFloat(1000),
			ContractVolumeThreshold: decimal.NewFromFloat(5000),
			DirectCommissionRate:    decimal.NewFromFloat(0.35),
			IndirectCommissionRate:  decimal.NewFromFloat(0.07),
			ExtendedCommissionRate:  decimal.NewFromFloat(0.035),
			MemeFeeRebate:           decimal.NewFromFloat(0.1),
		},
		{
			ID:                      3,
			Name:                    "Lv3",
			MemeVolumeThreshold:     decimal.NewFromFloat(10000),
			ContractVolumeThreshold: decimal.NewFromFloat(50000),
			DirectCommissionRate:    decimal.NewFromFloat(0.40),
			IndirectCommissionRate:  decimal.NewFromFloat(0.10),
			ExtendedCommissionRate:  decimal.NewFromFloat(0.05),
			MemeFeeRebate:           decimal.NewFromFloat(0.2),
		},
	}
}

// TestAgentLevel represents a test agent level
type TestAgentLevel struct {
	ID                      uint
	Name                    string
	MemeVolumeThreshold     decimal.Decimal
	ContractVolumeThreshold decimal.Decimal
	DirectCommissionRate    decimal.Decimal
	IndirectCommissionRate  decimal.Decimal
	ExtendedCommissionRate  decimal.Decimal
	MemeFeeRebate           decimal.Decimal
}

// SetupIntegrationTest sets up an integration test environment
func SetupIntegrationTest(t *testing.T) (*TestHelper, *TestFixtures) {
	// Skip if not in integration test mode
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Initialize random seed
	rand.Seed(time.Now().UnixNano())

	helper := NewTestHelper(t)
	fixtures := NewTestFixtures()

	return helper, fixtures
}

// TeardownIntegrationTest cleans up after integration tests
func TeardownIntegrationTest() {
	// Cleanup logic would go here
	// For now, this is a no-op since we're not using real database connections
}

// MockConfig represents a mock configuration for testing
type MockConfig struct {
	DatabaseURL string
	TestMode    bool
}

// GetMockConfig returns a mock configuration
func GetMockConfig() *MockConfig {
	return &MockConfig{
		DatabaseURL: "postgres://test:test@localhost:5432/test_db",
		TestMode:    true,
	}
}
