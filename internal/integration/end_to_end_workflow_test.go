//go:build integration
// +build integration

package integration

import (
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestEndToEndAgentWorkflow tests the complete agent lifecycle from registration to earning commissions
func TestEndToEndAgentWorkflow(t *testing.T) {
	helper, fixtures := SetupIntegrationTest(t)
	defer TeardownIntegrationTest()

	t.Run("complete agent lifecycle workflow", func(t *testing.T) {
		// Phase 1: Agent Registration and Setup
		t.Log("=== Phase 1: Agent Registration ===")

		// Create new agent
		agentID := helper.GenerateTestUUID()
		agentEmail := helper.GenerateTestEmail()
		invitationCode := helper.GenerateTestInvitationCode()

		agent := &model.User{
			ID:             agentID,
			Email:          &agentEmail,
			InvitationCode: &invitationCode,
			AgentLevelID:   1, // Start at Level 1
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		helper.AssertEqual(uint(1), agent.AgentLevelID)
		if agent.InvitationCode != nil {
			helper.AssertNotEmpty(*agent.InvitationCode)
		}

		t.Logf("Agent created: %s with invitation code: %s", agentID, invitationCode)

		// Phase 2: Referral and User Acquisition
		t.Log("=== Phase 2: User Acquisition ===")

		// Agent refers 3 users
		referredUsers := make([]*model.User, 3)
		referrals := make([]*model.Referral, 3)
		for i := 0; i < 3; i++ {
			userID := helper.GenerateTestUUID()
			userEmail := helper.GenerateTestEmail()
			referredUsers[i] = &model.User{
				ID:           userID,
				Email:        &userEmail,
				AgentLevelID: 1,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}

			// Create referral relationship
			referrals[i] = &model.Referral{
				UserID:     userID,
				ReferrerID: &agentID,
				Depth:      1,
				CreatedAt:  time.Now(),
			}

			// Create wallet for each user
			walletAccountID := helper.GenerateTestUUID()
			walletID := helper.GenerateTestUUID()
			wallet := &model.UserWallet{
				ID:              helper.GenerateTestUUID(),
				UserID:          userID,
				Chain:           model.ChainType("SOL"),
				WalletAddress:   "0x" + helper.GenerateTestUUID().String()[:40],
				WalletAccountID: &walletAccountID,
				WalletID:        &walletID,
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			}

			helper.AssertEqual(agentID, *referrals[i].ReferrerID)
			helper.AssertEqual(userID, wallet.UserID)

			t.Logf("User %d referred: %s with wallet: %s", i+1, userID, wallet.WalletAddress)
		}

		// Phase 3: Trading Activity and Volume Generation
		t.Log("=== Phase 3: Trading Activity ===")

		totalMemeVolume := decimal.Zero
		totalContractVolume := decimal.Zero
		totalCommissions := decimal.Zero

		// Simulate trading activity for each referred user
		for i, user := range referredUsers {
			// Meme trading
			memeAmount := decimal.NewFromFloat(float64(2000 + i*1000)) // $2k, $3k, $4k
			memeFee := memeAmount.Mul(decimal.NewFromFloat(0.009))     // 0.9% fee

			memeTransaction := &model.AffiliateTransaction{
				ID:              uint(i + 1),
				OrderID:         helper.GenerateTestUUID(),
				UserID:          user.ID,
				TxHash:          "0x" + helper.GenerateTestUUID().String(),
				VolumeUSD:       memeAmount,
				TotalFee:        memeFee,
				Status:          model.StatusCompleted,
				TransactionType: model.Buy,
				Type:            model.Market,
				ChainID:         "solana",
				BaseAddress:     "0x123",
				BaseSymbol:      "MEME",
				QuoteAddress:    "0x456",
				QuoteSymbol:     "SOL",
				UserAddress:     "0x789",
				BaseAmount:      memeAmount,
				QuoteAmount:     memeAmount,
				Slippage:        decimal.NewFromFloat(0.01),
				CreatedAt:       time.Now(),
				UpdatedAt:       time.Now(),
			}

			totalMemeVolume = totalMemeVolume.Add(memeAmount)

			// Contract trading
			contractAmount := decimal.NewFromFloat(float64(10000 + i*5000)) // $10k, $15k, $20k
			contractFee := contractAmount.Mul(decimal.NewFromFloat(0.0055)) // 0.55% fee

			cloidValue := "contract_" + helper.GenerateTestUUID().String()
			contractTransaction := &model.HyperLiquidTransaction{
				Cloid:    cloidValue,
				UserID:   &user.ID,
				Size:     &contractAmount,
				BuildFee: &contractFee,
				Status:   helper.StringToPointer("filled"),
			}

			totalContractVolume = totalContractVolume.Add(contractAmount)

			// Calculate commissions for agent
			level1DirectRate := decimal.NewFromFloat(0.30) // 30% direct commission

			memeCommission := memeFee.Mul(level1DirectRate)
			contractCommission := contractFee.Mul(level1DirectRate)

			totalCommissions = totalCommissions.Add(memeCommission).Add(contractCommission)

			// Create commission ledger entries
			commissions := []*model.CommissionLedger{
				{
					ID:                    helper.GenerateTestUUID(),
					RecipientUserID:       agentID,
					SourceUserID:          user.ID,
					SourceTransactionID:   memeTransaction.TxHash,
					SourceTransactionType: "MEME",
					CommissionAmount:      memeCommission,
					CommissionAsset:       "SOL",
					Status:                "PENDING_CLAIM",
					CreatedAt:             &[]time.Time{time.Now()}[0],
					UpdatedAt:             &[]time.Time{time.Now()}[0],
				},
				{
					ID:                    helper.GenerateTestUUID(),
					RecipientUserID:       agentID,
					SourceUserID:          user.ID,
					SourceTransactionID:   contractTransaction.Cloid,
					SourceTransactionType: "CONTRACT",
					CommissionAmount:      contractCommission,
					CommissionAsset:       "USDC",
					Status:                "PENDING_CLAIM",
					CreatedAt:             &[]time.Time{time.Now()}[0],
					UpdatedAt:             &[]time.Time{time.Now()}[0],
				},
			}

			for _, commission := range commissions {
				helper.AssertEqual(agentID, commission.RecipientUserID)
				helper.AssertEqual(user.ID, commission.SourceUserID)
				helper.AssertEqual("PENDING_CLAIM", commission.Status)
			}

			t.Logf("User %d trading: Meme $%s (fee $%s), Contract $%s (fee $%s)",
				i+1, memeAmount.String(), memeFee.String(),
				contractAmount.String(), contractFee.String())
		}

		// Phase 4: Level Upgrade Based on Volume
		t.Log("=== Phase 4: Level Upgrade ===")

		// Check if agent qualifies for level upgrade
		level2MemeThreshold := decimal.NewFromFloat(1000)
		level2ContractThreshold := decimal.NewFromFloat(5000)
		level3MemeThreshold := decimal.NewFromFloat(10000)
		level3ContractThreshold := decimal.NewFromFloat(50000)

		var newLevel uint = 1
		if totalMemeVolume.GreaterThanOrEqual(level3MemeThreshold) ||
			totalContractVolume.GreaterThanOrEqual(level3ContractThreshold) {
			newLevel = 3
		} else if totalMemeVolume.GreaterThanOrEqual(level2MemeThreshold) ||
			totalContractVolume.GreaterThanOrEqual(level2ContractThreshold) {
			newLevel = 2
		}

		// Upgrade agent level
		if newLevel > agent.AgentLevelID {
			oldLevel := agent.AgentLevelID
			agent.AgentLevelID = newLevel
			agent.UpdatedAt = time.Now()

			t.Logf("Agent upgraded from Level %d to Level %d", oldLevel, newLevel)
			t.Logf("Qualifying volumes: Meme $%s, Contract $%s",
				totalMemeVolume.String(), totalContractVolume.String())
		}

		helper.AssertTrue(agent.AgentLevelID >= 2) // Should be at least Level 2

		// Phase 5: Commission Claiming
		t.Log("=== Phase 5: Commission Claiming ===")

		// Simulate claiming all pending commissions
		claimTime := time.Now()
		claimedAmount := decimal.Zero

		// In a real scenario, we would query the database for pending commissions
		// Here we simulate the claiming process
		simulatedPendingCommissions := 6 // 2 per user * 3 users
		avgCommissionAmount := totalCommissions.Div(decimal.NewFromFloat(float64(simulatedPendingCommissions)))

		for i := 0; i < simulatedPendingCommissions; i++ {
			commission := &model.CommissionLedger{
				ID:               helper.GenerateTestUUID(),
				RecipientUserID:  agentID,
				SourceUserID:     referredUsers[i%3].ID,
				CommissionAmount: avgCommissionAmount,
				Status:           "PENDING_CLAIM",
				CreatedAt:        &[]time.Time{time.Now().Add(-time.Hour)}[0],
				UpdatedAt:        &[]time.Time{time.Now().Add(-time.Hour)}[0],
			}

			// Claim commission
			commission.Status = "CLAIMED"
			commission.ClaimedAt = &claimTime
			claimedAmount = claimedAmount.Add(commission.CommissionAmount)

			helper.AssertEqual("CLAIMED", commission.Status)
			helper.AssertNotNil(commission.ClaimedAt)
		}

		helper.AssertTrue(claimedAmount.GreaterThan(decimal.Zero))

		// Phase 6: Performance Analytics
		t.Log("=== Phase 6: Performance Summary ===")

		// Calculate key metrics
		referralCount := len(referredUsers)
		avgVolumePerUser := totalMemeVolume.Add(totalContractVolume).Div(decimal.NewFromFloat(float64(referralCount)))
		commissionRate := totalCommissions.Div(totalMemeVolume.Add(totalContractVolume)).Mul(decimal.NewFromFloat(100))

		// Verify business metrics
		helper.AssertEqual(3, referralCount)
		helper.AssertTrue(totalMemeVolume.GreaterThan(decimal.Zero))
		helper.AssertTrue(totalContractVolume.GreaterThan(decimal.Zero))
		helper.AssertTrue(totalCommissions.GreaterThan(decimal.Zero))
		helper.AssertTrue(claimedAmount.GreaterThan(decimal.Zero))

		// Performance summary
		t.Logf("=== AGENT PERFORMANCE SUMMARY ===")
		t.Logf("Agent ID: %s", agentID)
		t.Logf("Final Level: %d", agent.AgentLevelID)
		t.Logf("Referrals: %d users", referralCount)
		t.Logf("Total Meme Volume: $%s", totalMemeVolume.String())
		t.Logf("Total Contract Volume: $%s", totalContractVolume.String())
		t.Logf("Total Volume: $%s", totalMemeVolume.Add(totalContractVolume).String())
		t.Logf("Average Volume per User: $%s", avgVolumePerUser.String())
		t.Logf("Total Commissions Earned: $%s", totalCommissions.String())
		t.Logf("Total Commissions Claimed: $%s", claimedAmount.String())
		t.Logf("Effective Commission Rate: %s%%", commissionRate.String())

		// Verify end-to-end workflow success
		helper.AssertTrue(agent.AgentLevelID > 1)                                                            // Agent upgraded
		helper.AssertTrue(referralCount > 0)                                                                 // Has referrals
		helper.AssertTrue(totalMemeVolume.Add(totalContractVolume).GreaterThan(decimal.NewFromFloat(10000))) // Significant volume
		helper.AssertTrue(totalCommissions.GreaterThan(decimal.NewFromFloat(100)))                           // Meaningful commissions
		helper.AssertTrue(claimedAmount.GreaterThan(decimal.Zero))                                           // Successfully claimed
	})

	t.Run("multi-level referral network", func(t *testing.T) {
		// Test a complex multi-level referral network
		t.Log("=== Multi-Level Referral Network Test ===")

		// Create 4-level referral chain
		level1Agent := helper.GenerateTestUUID()  // Top agent
		level2Agent := helper.GenerateTestUUID()  // Referred by level1
		level3Agent := helper.GenerateTestUUID()  // Referred by level2
		level4Trader := helper.GenerateTestUUID() // Referred by level3, generates transactions

		// Create referral chain
		_ = []*model.User{
			{ID: level1Agent, AgentLevelID: 4},
			{ID: level2Agent, AgentLevelID: 3},
			{ID: level3Agent, AgentLevelID: 2},
			{ID: level4Trader, AgentLevelID: 1},
		}

		// Create referral relationships
		referrals := []*model.Referral{
			{UserID: level2Agent, ReferrerID: &level1Agent, Depth: 1},
			{UserID: level3Agent, ReferrerID: &level2Agent, Depth: 1},
			{UserID: level4Trader, ReferrerID: &level3Agent, Depth: 1},
		}

		// Verify referral chain
		helper.AssertEqual(level1Agent, *referrals[0].ReferrerID) // Level 2 → Level 1
		helper.AssertEqual(level2Agent, *referrals[1].ReferrerID) // Level 3 → Level 2
		helper.AssertEqual(level3Agent, *referrals[2].ReferrerID) // Level 4 → Level 3

		// Generate transaction from bottom level
		transactionAmount := decimal.NewFromFloat(10000)
		transactionFee := transactionAmount.Mul(decimal.NewFromFloat(0.0055))

		// Calculate multi-level commissions
		level4Rates := &model.AgentLevel{
			DirectCommissionRate:   decimal.NewFromFloat(0.40), // 40%
			IndirectCommissionRate: decimal.NewFromFloat(0.10), // 10%
			ExtendedCommissionRate: decimal.NewFromFloat(0.05), // 5%
		}

		directCommission := transactionFee.Mul(level4Rates.DirectCommissionRate)     // Level 3 gets 40%
		indirectCommission := transactionFee.Mul(level4Rates.IndirectCommissionRate) // Level 2 gets 10%
		extendedCommission := transactionFee.Mul(level4Rates.ExtendedCommissionRate) // Level 1 gets 5%

		totalDistributed := directCommission.Add(indirectCommission).Add(extendedCommission)
		distributionRate := totalDistributed.Div(transactionFee).Mul(decimal.NewFromFloat(100))

		helper.AssertTrue(directCommission.GreaterThan(indirectCommission))
		helper.AssertTrue(indirectCommission.GreaterThan(extendedCommission))
		helper.AssertTrue(distributionRate.LessThanOrEqual(decimal.NewFromFloat(100)))

		t.Logf("Multi-level commission distribution:")
		t.Logf("- Transaction fee: $%s", transactionFee.String())
		t.Logf("- Direct (Level 3): $%s (40%%)", directCommission.String())
		t.Logf("- Indirect (Level 2): $%s (10%%)", indirectCommission.String())
		t.Logf("- Extended (Level 1): $%s (5%%)", extendedCommission.String())
		t.Logf("- Total distributed: $%s (%s%% of fee)", totalDistributed.String(), distributionRate.String())
	})

	_ = fixtures
}
