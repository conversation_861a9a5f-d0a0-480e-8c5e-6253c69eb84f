package contract

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

type ContractCommissionService struct {
	userRepo       repo.InvitationRepo
	levelRepo      repo.LevelRepo
	commissionRepo transaction.CommissionLedgerRepositoryInterface
}

func NewContractCommissionService() *ContractCommissionService {
	return &ContractCommissionService{
		userRepo:       &rebate.InvitationRepository{},
		levelRepo:      repo.NewLevelRepository(),
		commissionRepo: transaction.NewCommissionLedgerRepository(),
	}
}

// Process contract trading commission calculations
func (s *ContractCommissionService) ProcessContractCommission(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction) error {
	global.GVA_LOG.Info("Processing contract commission",
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.String("user_id", hyperliquidTx.UserID.String()),
		zap.String("status", *hyperliquidTx.Status))

	if *hyperliquidTx.Status != "filled" {
		global.GVA_LOG.Debug("Skipping non-filled transaction",
			zap.String("cloid", hyperliquidTx.Cloid),
			zap.String("status", *hyperliquidTx.Status))
		return nil
	}

	err := s.processCommission(ctx, hyperliquidTx)
	if err != nil {
		return fmt.Errorf("failed to process commission distribution: %w", err)
	}

	global.GVA_LOG.Info("Contract commission processed successfully",
		zap.String("cloid", hyperliquidTx.Cloid),
		zap.String("user_id", hyperliquidTx.UserID.String()),
	)

	return nil
}

func (s *ContractCommissionService) processCommission(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	referralInfo, err := s.getReferralInfo(ctx, *tx.UserID)
	if err != nil {
		global.GVA_LOG.Debug("No referral found for user",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("User has no referrer",
			zap.String("user_id", tx.UserID.String()),
			zap.String("cloid", tx.Cloid))
		return nil
	}

	// Get the upline hierarchy for the user
	if tx.BuildFee != nil {
		 err := s.getUplineHierarchy(ctx, *tx.UserID, tx)
		if err != nil {
			return fmt.Errorf("failed to get upline hierarchy: %w", err)
		}
	}

	return nil
}

// getUserWithLevel gets user with their agent level information
func (s *ContractCommissionService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// getUplineHierarchy gets the upline hierarchy for a user (up to 3 levels)
func (s *ContractCommissionService) getUplineHierarchy(ctx context.Context, userID uuid.UUID, tx *model.HyperLiquidTransaction)  error {
	// Get direct referrer (L1)
	referralInfo, err := s.getReferralInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get referral info: %w", err)
	}

	if referralInfo.ReferrerID == nil {
		return  nil
	}

	// Get L1 upline
	l1User, err := s.getUserWithLevel(ctx, *referralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	// get direct rebate by agent level
	// buildFee * DirectCommissionRate
	directLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l1User.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to get agent level: %w", err)
	}
	directFee := tx.BuildFee.Mul(directLevel.DirectCommissionRate)
	now := time.Now()
	commissionLedgerDirect := model.CommissionLedger{
		RecipientUserID:       l1User.ID,
		SourceUserID:          *tx.UserID,
		SourceTransactionID:   tx.Cloid,
		SourceTransactionType: "Direct",
		CommissionAmount:      directFee,
		CommissionAsset:       directLevel.DirectCommissionRate.String(),
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerDirect).Error; err != nil {
		return fmt.Errorf("failed to create commission direct: %w", err)
	}

	// Indirect Get L2 upline (referrer of L1)
	l2ReferralInfo, err := s.getReferralInfo(ctx, *referralInfo.ReferrerID)
	if err != nil || l2ReferralInfo.ReferrerID == nil {
		return fmt.Errorf("failed to referral info indirect: %w", err)
	}

	l2User, err := s.getUserWithLevel(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to user level indirect: %w", err)
	}
	// buildFee * IndirectCommissionRate
	indirectLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l2User.AgentLevelID)
	if err != nil {
		return  fmt.Errorf("failed to agent level indirect: %w", err)
	}
	indirectFee := tx.BuildFee.Mul(indirectLevel.IndirectCommissionRate)
	commissionLedgerIndirect := model.CommissionLedger{
		RecipientUserID:       l2User.ID,
		SourceUserID:          *tx.UserID,
		SourceTransactionID:   tx.Cloid,
		SourceTransactionType: "Indirect",
		CommissionAmount:      indirectFee,
		CommissionAsset:       indirectLevel.IndirectCommissionRate.String(),
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerIndirect).Error; err != nil {
		return fmt.Errorf("failed to create commission indirect: %w", err)
	}

	// Get L3 upline (referrer of L2)
	l3ReferralInfo, err := s.getReferralInfo(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil || l3ReferralInfo.ReferrerID == nil {
		return fmt.Errorf("failed to referral info extended: %w", err)
	}

	l3User, err := s.getUserWithLevel(ctx, *l3ReferralInfo.ReferrerID)
	if err != nil {
		return  fmt.Errorf("failed to user level extended: %w", err)
	}

	// buildFee * extendedCommissionRate
	extendedLevel, err := s.levelRepo.GetAgentLevelByID(ctx, l3User.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to agent level extended: %w", err)
	}
	extendedFee := tx.BuildFee.Mul(extendedLevel.ExtendedCommissionRate)
	commissionLedgerExtended := model.CommissionLedger{
		RecipientUserID:       l3User.ID,
		SourceUserID:          *tx.UserID,
		SourceTransactionID:   tx.Cloid,
		SourceTransactionType: "Extended",
		CommissionAmount:      extendedFee,
		CommissionAsset:       extendedLevel.ExtendedCommissionRate.String(),
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	if err := global.GVA_DB.WithContext(ctx).Create(commissionLedgerExtended).Error; err != nil {
		return fmt.Errorf("failed to create commission extended: %w", err)
	}

	return  nil
}

// getReferralInfo gets referral information for a user
func (s *ContractCommissionService) getReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&referral).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}
