package level

import (
	"context"
	"errors"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

// MockLevelRepo is a mock implementation of repo.LevelRepo
type MockLevelRepo struct {
	mock.Mock
}

func (m *MockLevelRepo) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

func TestNewLevelService(t *testing.T) {
	// Act
	service := NewLevelService()

	// Assert
	require.NotNil(t, service)
	assert.IsType(t, &LevelService{}, service)
}

func TestLevelService_InterfaceCompliance(t *testing.T) {
	// This test ensures that LevelService implements service.LevelI
	var _ service.LevelI = (*LevelService)(nil)
}

func TestLevelService_GetAgentLevels(t *testing.T) {
	tests := []struct {
		name          string
		setupMock     func(*MockLevelRepo)
		expectedError string
		expectedCount int
	}{
		{
			name: "successful retrieval of agent levels",
			setupMock: func(mockRepo *MockLevelRepo) {
				levels := []model.AgentLevel{
					{ID: 1, Name: "Lv1", DirectCommissionRate: decimal.NewFromFloat(0.1)},
					{ID: 2, Name: "Lv2", DirectCommissionRate: decimal.NewFromFloat(0.15)},
					{ID: 3, Name: "Lv3", DirectCommissionRate: decimal.NewFromFloat(0.2)},
				}
				mockRepo.On("GetAgentLevels", mock.Anything).Return(levels, nil)
			},
			expectedCount: 3,
		},
		{
			name: "repository error",
			setupMock: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel(nil), errors.New("database error"))
			},
			expectedError: "database error",
		},
		{
			name: "empty result",
			setupMock: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel{}, nil)
			},
			expectedCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := &MockLevelRepo{}
			tt.setupMock(mockRepo)

			service := &LevelService{levelRepo: mockRepo}
			ctx := context.Background()

			// Act
			levels, err := service.GetAgentLevels(ctx)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, levels)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, levels)
				assert.Len(t, levels, tt.expectedCount)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLevelService_GetAgentLevelByID(t *testing.T) {
	tests := []struct {
		name          string
		levelID       uint
		setupMock     func(*MockLevelRepo, uint)
		expectedError string
		expectedLevel *model.AgentLevel
	}{
		{
			name:    "successful retrieval by ID",
			levelID: 1,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				level := &model.AgentLevel{ID: id, Name: "Lv1", DirectCommissionRate: decimal.NewFromFloat(0.1)}
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(level, nil)
			},
			expectedLevel: &model.AgentLevel{ID: 1, Name: "Lv1", DirectCommissionRate: decimal.NewFromFloat(0.1)},
		},
		{
			name:    "level not found",
			levelID: 999,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(nil, errors.New("record not found"))
			},
			expectedError: "record not found",
		},
		{
			name:    "repository error",
			levelID: 1,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(nil, errors.New("database error"))
			},
			expectedError: "database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := &MockLevelRepo{}
			tt.setupMock(mockRepo, tt.levelID)

			service := &LevelService{levelRepo: mockRepo}
			ctx := context.Background()

			// Act
			level, err := service.GetAgentLevelByID(ctx, tt.levelID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, level)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, level)
				assert.Equal(t, tt.expectedLevel.ID, level.ID)
				assert.Equal(t, tt.expectedLevel.Name, level.Name)
				assert.True(t, tt.expectedLevel.DirectCommissionRate.Equal(level.DirectCommissionRate))
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLevelService_UpdateLevelCommission(t *testing.T) {
	tests := []struct {
		name          string
		levelID       uint
		directRate    float64
		indirectRate  float64
		extendedRate  float64
		memeFeeRebate float64
		setupMock     func(*MockLevelRepo, uint)
		expectedError string
	}{
		{
			name:          "successful update with valid rates",
			levelID:       1,
			directRate:    0.15,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				level := &model.AgentLevel{ID: id, Name: "Lv1", DirectCommissionRate: decimal.NewFromFloat(0.1)}
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(level, nil)
				mockRepo.On("UpdateAgentLevel", mock.Anything, mock.AnythingOfType("*model.AgentLevel")).Return(nil)
			},
		},
		{
			name:          "invalid direct rate - negative",
			levelID:       1,
			directRate:    -0.1,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock:     func(mockRepo *MockLevelRepo, id uint) {},
			expectedError: "direct commission rate must be between 0 and 1",
		},
		{
			name:          "invalid direct rate - greater than 1",
			levelID:       1,
			directRate:    1.5,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock:     func(mockRepo *MockLevelRepo, id uint) {},
			expectedError: "direct commission rate must be between 0 and 1",
		},
		{
			name:          "invalid indirect rate - negative",
			levelID:       1,
			directRate:    0.15,
			indirectRate:  -0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock:     func(mockRepo *MockLevelRepo, id uint) {},
			expectedError: "indirect commission rate must be between 0 and 1",
		},
		{
			name:          "invalid extended rate - greater than 1",
			levelID:       1,
			directRate:    0.15,
			indirectRate:  0.05,
			extendedRate:  1.5,
			memeFeeRebate: 0.1,
			setupMock:     func(mockRepo *MockLevelRepo, id uint) {},
			expectedError: "extended commission rate must be between 0 and 1",
		},
		{
			name:          "invalid meme fee rebate - negative",
			levelID:       1,
			directRate:    0.15,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: -0.1,
			setupMock:     func(mockRepo *MockLevelRepo, id uint) {},
			expectedError: "meme fee rebate must be between 0 and 1",
		},
		{
			name:          "level not found",
			levelID:       999,
			directRate:    0.15,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(nil, errors.New("record not found"))
			},
			expectedError: "failed to get agent level",
		},
		{
			name:          "update error",
			levelID:       1,
			directRate:    0.15,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.1,
			setupMock: func(mockRepo *MockLevelRepo, id uint) {
				level := &model.AgentLevel{ID: id, Name: "Lv1", DirectCommissionRate: decimal.NewFromFloat(0.1)}
				mockRepo.On("GetAgentLevelByID", mock.Anything, id).Return(level, nil)
				mockRepo.On("UpdateAgentLevel", mock.Anything, mock.AnythingOfType("*model.AgentLevel")).Return(errors.New("update failed"))
			},
			expectedError: "failed to update agent level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockRepo := &MockLevelRepo{}
			tt.setupMock(mockRepo, tt.levelID)

			service := &LevelService{levelRepo: mockRepo}
			ctx := context.Background()

			// Act
			level, err := service.UpdateLevelCommission(ctx, tt.levelID, tt.directRate, tt.indirectRate, tt.extendedRate, tt.memeFeeRebate)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, level)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, level)
				assert.True(t, level.DirectCommissionRate.Equal(decimal.NewFromFloat(tt.directRate)))
				assert.True(t, level.IndirectCommissionRate.Equal(decimal.NewFromFloat(tt.indirectRate)))
				assert.True(t, level.ExtendedCommissionRate.Equal(decimal.NewFromFloat(tt.extendedRate)))
				assert.True(t, level.MemeFeeRebate.Equal(decimal.NewFromFloat(tt.memeFeeRebate)))
			}

			mockRepo.AssertExpectations(t)
		})
	}
}
