package invitation

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewInvitationRecordService(t *testing.T) {
	// Act
	service := NewInvitationRecordService()

	// Assert
	require.NotNil(t, service)
	assert.IsType(t, &InvitationRecordService{}, service)
	assert.NotNil(t, service.userRepo)
	assert.NotNil(t, service.affiliateRepo)
	assert.NotNil(t, service.hyperLiquidRepo)
}

func TestInvitationRecord_Struct(t *testing.T) {
	// Test that InvitationRecord struct can be created and has expected fields
	record := &InvitationRecord{
		AcceptedInvitationAddress:      "0x123456789",
		AddressTransactionVolume:       decimal.NewFromFloat(1000.50),
		SubordinateInvitedAddressCount: 5,
		AddressHandlingFee:             decimal.NewFromFloat(10.25),
	}

	assert.Equal(t, "0x123456789", record.AcceptedInvitationAddress)
	assert.True(t, record.AddressTransactionVolume.Equal(decimal.NewFromFloat(1000.50)))
	assert.Equal(t, 5, record.SubordinateInvitedAddressCount)
	assert.True(t, record.AddressHandlingFee.Equal(decimal.NewFromFloat(10.25)))
}

func TestInvitationRecordService_SortRecordsByVolume(t *testing.T) {
	service := &InvitationRecordService{}

	tests := []struct {
		name     string
		records  []*InvitationRecord
		expected []*InvitationRecord
	}{
		{
			name:     "empty records",
			records:  []*InvitationRecord{},
			expected: []*InvitationRecord{},
		},
		{
			name: "single record",
			records: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
			expected: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
		},
		{
			name: "multiple records - already sorted",
			records: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(300.0),
				},
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
			expected: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(300.0),
				},
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
		},
		{
			name: "multiple records - needs sorting",
			records: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(300.0),
				},
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
			},
			expected: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(300.0),
				},
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
		},
		{
			name: "records with equal volumes",
			records: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
			},
			expected: []*InvitationRecord{
				{
					AcceptedInvitationAddress: "0x3",
					AddressTransactionVolume:  decimal.NewFromFloat(200.0),
				},
				{
					AcceptedInvitationAddress: "0x1",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
				{
					AcceptedInvitationAddress: "0x2",
					AddressTransactionVolume:  decimal.NewFromFloat(100.0),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy to avoid modifying the original test data
			recordsCopy := make([]*InvitationRecord, len(tt.records))
			copy(recordsCopy, tt.records)

			// Act
			service.sortRecordsByVolume(recordsCopy)

			// Assert
			require.Len(t, recordsCopy, len(tt.expected))
			for i, expected := range tt.expected {
				assert.Equal(t, expected.AcceptedInvitationAddress, recordsCopy[i].AcceptedInvitationAddress)
				assert.True(t, expected.AddressTransactionVolume.Equal(recordsCopy[i].AddressTransactionVolume))
			}
		})
	}
}

func TestInvitationRecordService_Methods(t *testing.T) {
	t.Run("constructor creates valid instance", func(t *testing.T) {
		service := NewInvitationRecordService()
		require.NotNil(t, service)

		// Verify it's the correct type
		assert.IsType(t, &InvitationRecordService{}, service)
	})
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestInvitationRecordService_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}
