package reward

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

// ClaimService handles reward claiming operations
type ClaimService struct {
	commissionRepo   transaction.CommissionLedgerRepositoryInterface
	memeCashbackRepo transaction.MemeCashbackRepositoryInterface
}

// NewClaimService creates a new claim service
func NewClaimService() service.ClaimI {
	return &ClaimService{
		commissionRepo:   transaction.NewCommissionLedgerRepository(),
		memeCashbackRepo: transaction.NewMemeCashbackRepository(),
	}
}

func (s *ClaimService) GetClaimReward(ctx context.Context, userID uuid.UUID) (*response.ClaimRewardResponse, error) {
	pendingCashbacks, err := s.memeCashbackRepo.GetPendingCashbacksByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending rebate", zap.Error(err))
		return nil, fmt.Errorf("failed to get pending rebate: %w", err)
	}

	// Calculate total amounts
	// var totalCashbackUSD decimal.Decimal
	var totalCashbackSOL decimal.Decimal
	cashbackIDs := make([]uuid.UUID, 0, len(pendingCashbacks))
	for _, cashback := range pendingCashbacks {
		cashbackIDs = append(cashbackIDs, cashback.ID)
		totalCashbackSOL = totalCashbackSOL.Add(cashback.CashbackAmountSOL)
		// totalCashbackUSD = totalCashbackUSD.Add(cashback.CashbackAmountUSD)
	}

	pendingCommissions, err := s.commissionRepo.GetPendingCommissionsByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending rebate", zap.Error(err))
		return nil, fmt.Errorf("failed to get pending rebate: %w", err)
	}

	// Calculate total amounts
	var totalCommissionUSD decimal.Decimal
	for _, commission := range pendingCommissions {
		totalCommissionUSD = totalCommissionUSD.Add(commission.CommissionAmount)
	}

	return &response.ClaimRewardResponse{
		ClaimMeme:     totalCashbackSOL.String(),
		ClaimContract: totalCommissionUSD.String(),
	}, nil
}

// ClaimMemeReward claims all pending MEME rewards for a user
func (s *ClaimService) ClaimMemeReward(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Starting MEME reward claim process", zap.String("user_id", userID.String()))

	// Get pending MEME cashbacks
	pendingCashbacks, err := s.memeCashbackRepo.GetPendingCashbacksByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending MEME cashbacks", zap.Error(err))
		return fmt.Errorf("failed to get pending MEME cashbacks: %w", err)
	}

	// Calculate total amounts
	var totalCashbackUSD decimal.Decimal
	var totalCashbackSOL decimal.Decimal

	cashbackIDs := make([]uuid.UUID, 0, len(pendingCashbacks))
	for _, cashback := range pendingCashbacks {
		cashbackIDs = append(cashbackIDs, cashback.ID)
		totalCashbackSOL = totalCashbackSOL.Add(cashback.CashbackAmountSOL)
		totalCashbackUSD = totalCashbackUSD.Add(cashback.CashbackAmountUSD)
	}

	// Validate that we have something to claim
	if totalCashbackUSD.IsZero() && totalCashbackSOL.IsZero() {
		return fmt.Errorf("no pending rewards to claim")
	}

	// Start database transaction
	tx := global.GVA_DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update cashback status to CLAIMED
	if len(cashbackIDs) > 0 {
		now := time.Now()
		err = tx.Model(&model.MemeCashback{}).
			Where("id IN ? AND status = ?", cashbackIDs, "PENDING_CLAIM").
			Updates(map[string]interface{}{
				"status":     "CLAIMED",
				"claimed_at": &now,
				"updated_at": &now,
			}).Error

		if err != nil {
			tx.Rollback()
			global.GVA_LOG.Error("Failed to update cashback status", zap.Error(err))
			return fmt.Errorf("failed to update cashback status: %w", err)
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("Failed to commit transaction", zap.Error(err))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// TODO: Call external transfer API here
	// This is where you would integrate with your transfer service
	// For now, we'll just log the transfer details
	global.GVA_LOG.Info("Transfer details for MEME reward claim",
		zap.String("user_id", userID.String()),
		zap.String("cashback_usd", totalCashbackUSD.String()),
		zap.String("cashback_sol", totalCashbackSOL.String()),
	)

	return nil
}

// ClaimContractReward claims all pending CONTRACT rewards for a user
func (s *ClaimService) ClaimContractReward(ctx context.Context, userID uuid.UUID) error {
	global.GVA_LOG.Info("Starting CONTRACT reward claim process", zap.String("user_id", userID.String()))

	// Get pending CONTRACT commissions
	pendingCommissions, err := s.commissionRepo.GetPendingCommissionsByUserID(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get pending CONTRACT commissions", zap.Error(err))
		return fmt.Errorf("failed to get pending CONTRACT commissions: %w", err)
	}

	if len(pendingCommissions) == 0 {
		return fmt.Errorf("no pending CONTRACT commissions found for user")
	}

	// Calculate total amounts
	var totalCommissionUSD decimal.Decimal

	commissionIDs := make([]uuid.UUID, 0, len(pendingCommissions))
	for _, commission := range pendingCommissions {
		commissionIDs = append(commissionIDs, commission.ID)
		totalCommissionUSD = totalCommissionUSD.Add(commission.CommissionAmount)
	}

	// Validate that we have something to claim
	if totalCommissionUSD.IsZero() {
		return fmt.Errorf("no pending CONTRACT rewards to claim")
	}

	// Start database transaction
	tx := global.GVA_DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update commission status to CLAIMED
	now := time.Now()
	err = tx.Model(&model.CommissionLedger{}).
		Where("id IN ? AND status = ?", commissionIDs, "PENDING_CLAIM").
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": &now,
			"updated_at": &now,
		}).Error

	if err != nil {
		tx.Rollback()
		global.GVA_LOG.Error("Failed to update commission status", zap.Error(err))
		return fmt.Errorf("failed to update commission status: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		global.GVA_LOG.Error("Failed to commit transaction", zap.Error(err))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// TODO: Call external transfer API here
	// This is where you would integrate with your transfer service
	// For now, we'll just log the transfer details
	global.GVA_LOG.Info("Transfer details for CONTRACT reward claim",
		zap.String("user_id", userID.String()),
		zap.String("commission_usd", totalCommissionUSD.String()),
	)

	return nil
}
