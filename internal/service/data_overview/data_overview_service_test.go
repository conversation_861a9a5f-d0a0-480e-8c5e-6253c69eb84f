package data_overview

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
)

func TestNewDataOverviewService(t *testing.T) {
	// Act
	service := NewDataOverviewService()

	// Assert
	require.NotNil(t, service)
	assert.IsType(t, &DataOverviewService{}, service)
}

func TestDataOverviewService_InterfaceCompliance(t *testing.T) {
	// This test ensures that DataOverviewService implements DataOverviewServiceInterface
	var _ DataOverviewServiceInterface = (*DataOverviewService)(nil)
}

func TestDataOverviewService_GetDataOverview_InvalidTimeRange(t *testing.T) {
	t.Skip("Skipping database-dependent test - requires integration test setup")
}

func TestDataOverviewService_CalculateSummary(t *testing.T) {
	service := &DataOverviewService{}

	tests := []struct {
		name     string
		data     []*response.DataOverview
		expected *response.DataOverviewSummary
	}{
		{
			name: "empty data",
			data: []*response.DataOverview{},
			expected: &response.DataOverviewSummary{
				TotalRebateAmount:        0,
				TotalTransactionVolume:   0,
				TotalInvitationCount:     0,
				PeakRebateAmount:         0,
				PeakTransactionVolume:    0,
				PeakInvitationCount:      0,
				AverageRebateAmount:      0,
				AverageTransactionVolume: 0,
				AverageInvitationCount:   0,
			},
		},
		{
			name: "single data point",
			data: []*response.DataOverview{
				{
					RebateAmount:      100.50,
					TransactionVolume: 1000.75,
					InvitationCount:   5,
					Timestamp:         time.Now(),
					Period:            "01/01",
				},
			},
			expected: &response.DataOverviewSummary{
				TotalRebateAmount:        100.50,
				TotalTransactionVolume:   1000.75,
				TotalInvitationCount:     5,
				PeakRebateAmount:         100.50,
				PeakTransactionVolume:    1000.75,
				PeakInvitationCount:      5,
				AverageRebateAmount:      100.50,
				AverageTransactionVolume: 1000.75,
				AverageInvitationCount:   5.0,
			},
		},
		{
			name: "multiple data points",
			data: []*response.DataOverview{
				{
					RebateAmount:      100.0,
					TransactionVolume: 1000.0,
					InvitationCount:   5,
					Timestamp:         time.Now(),
					Period:            "01/01",
				},
				{
					RebateAmount:      200.0,
					TransactionVolume: 2000.0,
					InvitationCount:   10,
					Timestamp:         time.Now(),
					Period:            "01/02",
				},
				{
					RebateAmount:      150.0,
					TransactionVolume: 1500.0,
					InvitationCount:   7,
					Timestamp:         time.Now(),
					Period:            "01/03",
				},
			},
			expected: &response.DataOverviewSummary{
				TotalRebateAmount:        450.0,
				TotalTransactionVolume:   4500.0,
				TotalInvitationCount:     22,
				PeakRebateAmount:         200.0,
				PeakTransactionVolume:    2000.0,
				PeakInvitationCount:      10,
				AverageRebateAmount:      150.0,
				AverageTransactionVolume: 1500.0,
				AverageInvitationCount:   7.33,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act
			result := service.calculateSummary(tt.data)

			// Assert
			assert.Equal(t, tt.expected.TotalRebateAmount, result.TotalRebateAmount)
			assert.Equal(t, tt.expected.TotalTransactionVolume, result.TotalTransactionVolume)
			assert.Equal(t, tt.expected.TotalInvitationCount, result.TotalInvitationCount)
			assert.Equal(t, tt.expected.PeakRebateAmount, result.PeakRebateAmount)
			assert.Equal(t, tt.expected.PeakTransactionVolume, result.PeakTransactionVolume)
			assert.Equal(t, tt.expected.PeakInvitationCount, result.PeakInvitationCount)
			assert.Equal(t, tt.expected.AverageRebateAmount, result.AverageRebateAmount)
			assert.Equal(t, tt.expected.AverageTransactionVolume, result.AverageTransactionVolume)
			assert.Equal(t, tt.expected.AverageInvitationCount, result.AverageInvitationCount)
		})
	}
}

func TestDataOverviewService_TimeRangeValidation(t *testing.T) {
	t.Skip("Skipping database-dependent test - requires integration test setup")
}

func TestDataOverviewService_DataTypeValidation(t *testing.T) {
	t.Skip("Skipping database-dependent test - requires integration test setup")
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestDataOverviewService_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}
