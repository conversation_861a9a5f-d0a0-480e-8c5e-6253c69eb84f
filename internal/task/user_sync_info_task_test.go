package task

import (
	"encoding/json"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestConsumeUserSyncInfoEvent_ValidEvent(t *testing.T) {
	t.Skip("Skipping test that requires database - should be covered by integration tests")
}

func TestConsumeUserSyncInfoEvent_InvalidJSON(t *testing.T) {
	t.Skip("Skipping test that requires global logger - should be covered by integration tests")
}

func TestConsumeUserSyncInfoEvent_EmptyData(t *testing.T) {
	t.Skip("Skipping test that requires global logger - should be covered by integration tests")
}

func TestConsumeUserSyncInfoEvent_NilMessage(t *testing.T) {
	t.Skip("Skipping test that requires global logger - should be covered by integration tests")
}

func TestUserNewWalletEvent_Struct(t *testing.T) {
	// Test that UserNewWalletEvent struct can be created and marshaled/unmarshaled
	userID := uuid.New()
	event := UserNewWalletEvent{
		UserID: userID,
		Wallets: []UserWalletInfo{
			{
				Chain:           "SOL",
				WalletAddress:   "0x1234567890abcdef",
				WalletAccountID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				WalletID:        uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			},
		},
	}

	// Test marshaling
	data, err := json.Marshal(event)
	require.NoError(t, err)
	assert.NotEmpty(t, data)

	// Test unmarshaling
	var unmarshaled UserNewWalletEvent
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, event.UserID, unmarshaled.UserID)
	assert.Len(t, unmarshaled.Wallets, 1)
	assert.Equal(t, event.Wallets[0].Chain, unmarshaled.Wallets[0].Chain)
	assert.Equal(t, event.Wallets[0].WalletAddress, unmarshaled.Wallets[0].WalletAddress)
	assert.Equal(t, event.Wallets[0].WalletAccountID, unmarshaled.Wallets[0].WalletAccountID)
	assert.Equal(t, event.Wallets[0].WalletID, unmarshaled.Wallets[0].WalletID)
}

func TestUserWalletInfo_Struct(t *testing.T) {
	// Test UserWalletInfo struct
	wallet := UserWalletInfo{
		Chain:           "SOL",
		WalletAddress:   "0x1234567890abcdef",
		WalletAccountID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
		WalletID:        uuid.MustParse("550e8400-e29b-41d4-a716-************"),
	}

	assert.Equal(t, model.ChainType("SOL"), wallet.Chain)
	assert.Equal(t, "0x1234567890abcdef", wallet.WalletAddress)
	assert.Equal(t, uuid.MustParse("550e8400-e29b-41d4-a716-************"), wallet.WalletAccountID)
	assert.Equal(t, uuid.MustParse("550e8400-e29b-41d4-a716-************"), wallet.WalletID)
}

func TestUserWalletInfo_SupportedChains(t *testing.T) {
	supportedChains := []string{"SOL", "ETH", "BTC", "USDC"}

	for _, chain := range supportedChains {
		t.Run("chain_"+chain, func(t *testing.T) {
			wallet := UserWalletInfo{
				Chain:         model.ChainType(chain), // Convert string to ChainType
				WalletAddress: "0x1234567890abcdef",
			}

			assert.Equal(t, chain, string(wallet.Chain))
			assert.NotEmpty(t, wallet.WalletAddress)
		})
	}
}

func TestUserNewWalletEvent_MultipleWallets(t *testing.T) {
	// Test event with multiple wallets
	userID := uuid.New()
	event := UserNewWalletEvent{
		UserID: userID,
		Wallets: []UserWalletInfo{
			{
				Chain:           "SOL",
				WalletAddress:   "sol_address_123",
				WalletAccountID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				WalletID:        uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			},
			{
				Chain:           "ETH",
				WalletAddress:   "eth_address_456",
				WalletAccountID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				WalletID:        uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			},
			{
				Chain:           "BTC",
				WalletAddress:   "btc_address_789",
				WalletAccountID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				WalletID:        uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			},
		},
	}

	// Verify structure
	assert.Equal(t, userID, event.UserID)
	assert.Len(t, event.Wallets, 3)

	// Verify each wallet
	chains := []string{"SOL", "ETH", "BTC"}
	for i, expectedChain := range chains {
		assert.Equal(t, expectedChain, string(event.Wallets[i].Chain))
		// Just verify addresses are not empty (they don't need to contain chain name)
		assert.NotEmpty(t, event.Wallets[i].WalletAddress)
		// UUIDs are fixed, so we just verify they're not nil
		assert.NotEqual(t, uuid.Nil, event.Wallets[i].WalletAccountID)
		assert.NotEqual(t, uuid.Nil, event.Wallets[i].WalletID)
	}

	// Test JSON serialization with multiple wallets
	data, err := json.Marshal(event)
	require.NoError(t, err)

	var unmarshaled UserNewWalletEvent
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, event.UserID, unmarshaled.UserID)
	assert.Len(t, unmarshaled.Wallets, 3)
	for i := range event.Wallets {
		assert.Equal(t, event.Wallets[i], unmarshaled.Wallets[i])
	}
}

func TestUserNewWalletEvent_EmptyWallets(t *testing.T) {
	// Test event with no wallets
	userID := uuid.New()
	event := UserNewWalletEvent{
		UserID:  userID,
		Wallets: []UserWalletInfo{},
	}

	// Test JSON serialization
	data, err := json.Marshal(event)
	require.NoError(t, err)

	var unmarshaled UserNewWalletEvent
	err = json.Unmarshal(data, &unmarshaled)
	require.NoError(t, err)

	assert.Equal(t, event.UserID, unmarshaled.UserID)
	assert.Len(t, unmarshaled.Wallets, 0)
}

// Note: Integration tests with real NATS and database connections
// should be covered by integration test suite
func TestConsumeUserSyncInfoEvent_IntegrationTests(t *testing.T) {
	t.Skip("Skipping integration tests - these require NATS server and database setup")
}
