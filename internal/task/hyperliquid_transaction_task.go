package task

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/nats-io/nats.go"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/contract"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/transaction"
	"go.uber.org/zap"
)

func ConsumeHyperLiquidTransactionEvent(msgs []*nats.Msg) error {
	logger := global.GVA_LOG

	hyperLiquidTransactionService := transaction.NewHyperLiquidTransactionService()
	contractCommissionService := contract.NewContractCommissionService()

	for _, m := range msgs {
		var events HyperLiquidTransactionEventBatch
		if err := json.Unmarshal(m.Data, &events); err != nil {
			logger.Error("failed to unmarshal: %v", zap.Error(err))
			continue
		}

		for _, event := range events.Items {
			transactionEvent := model.HyperLiquidTransaction{
				Cloid:         event.Cloid,
				UserID:        &event.UserID,
				WalletAddress: event.WalletAddress,
				Side:          event.Side,
				OrderType:     event.OrderType,
				Symbol:        event.Symbol,
				IsBuy:         event.IsBuy,
				Leverage:      event.Leverage,
				Margin:        event.Margin,
				IsMarket:      event.IsMarket,
				TriggerPx:     event.TriggerPx,
				Tpsl:          event.Tpsl,
				Tif:           event.Tif,
				Base:          event.Base,
				Quote:         event.Quote,
				Size:          event.Size,
				Price:         event.Price,
				AvgPrice:      event.AvgPrice,
				BuildFee:      event.BuildFee,
				TotalFee:      event.TotalFee,
				FeeBp:         event.FeeBp,
				BuildAddress:  event.BuildAddress,
				Status:        event.Status,
				OID:           &event.OID,
				CreatedAt:     event.CreatedAt,
				TotalSz:       event.TotalSz,
				Hash:          event.Hash,
				Asset:         event.Asset,
				Coin:          event.Coin,
				ReduceOnly:    event.ReduceOnly,
				Grouping:      event.Grouping,
				Operation:     event.Operation,
			}

			existingTx, err := hyperLiquidTransactionService.FindHyperLiquidTransactionByCloid(context.Background(), event.Cloid)
			if err != nil {
				logger.Error("failed to find transaction by cloid",
					zap.String("cloid", event.Cloid),
					zap.Error(err),
				)
				continue
			}

			if existingTx != nil {
				err = hyperLiquidTransactionService.UpdateHyperLiquidTransactionByCloid(context.Background(), event.Cloid, &transactionEvent)
				if err != nil {
					logger.Error("failed to update hyperliquid transaction",
						zap.String("cloid", event.Cloid),
						zap.Error(err),
					)
					continue
				}
				logger.Info("updated hyperliquid transaction",
					zap.String("cloid", event.Cloid),
				)
			} else {
				err = hyperLiquidTransactionService.BulkInsertHyperLiquidTransactionsEvent(context.Background(), []model.HyperLiquidTransaction{transactionEvent})
				if err != nil {
					logger.Error("failed to create hyperliquid transaction",
						zap.String("cloid", event.Cloid),
						zap.Error(err),
					)
					continue
				}
				logger.Info("created hyperliquid transaction",
					zap.String("cloid", event.Cloid),
				)
			}

			if err := processContractCommission(context.Background(), &transactionEvent, contractCommissionService); err != nil {
				logger.Error("failed to process contract commission",
					zap.String("cloid", event.Cloid),
					zap.Error(err),
				)
				continue
			}
		}
	}

	return nil
}

func processContractCommission(ctx context.Context, hyperliquidTx *model.HyperLiquidTransaction, commissionService *contract.ContractCommissionService) error {
	if hyperliquidTx.Status == nil || *hyperliquidTx.Status != "filled" {
		return nil
	}

	if hyperliquidTx.UserID == nil {
		return nil
	}

	err := commissionService.ProcessContractCommission(ctx, hyperliquidTx)
	if err != nil {
		return fmt.Errorf("failed to process contract commission: %w", err)
	}


	return nil
}
