package resolvers

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
)

// MockClaimService is a mock implementation of service.ClaimI
type MockClaimService struct {
	mock.Mock
}

func (m *MockClaimService) GetClaimReward(ctx context.Context, userID uuid.UUID) (*response.ClaimRewardResponse, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*response.ClaimRewardResponse), args.Error(1)
}

func (m *MockClaimService) ClaimMemeReward(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockClaimService) ClaimContractReward(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func TestNewClaimResolver(t *testing.T) {
	// Act
	resolver := NewClaimResolver()

	// Assert
	require.NotNil(t, resolver)
	assert.IsType(t, &ClaimResolver{}, resolver)
	assert.NotNil(t, resolver.claimService)
}

func TestClaimResolver_GetClaimReward(t *testing.T) {
	tests := []struct {
		name           string
		userID         uuid.UUID
		setupMock      func(*MockClaimService)
		expectedResult *gql_model.ClaimRewardResponse
	}{
		{
			name:   "successful claim reward retrieval",
			userID: uuid.New(),
			setupMock: func(mockService *MockClaimService) {
				claimReward := &response.ClaimRewardResponse{
					ClaimMeme:     "100.50",
					ClaimContract: "250.75",
				}
				mockService.On("GetClaimReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(claimReward, nil)
			},
			expectedResult: &gql_model.ClaimRewardResponse{
				ClaimMeme:     "100.50",
				ClaimContract: "250.75",
			},
		},
		{
			name:   "user not logged in",
			userID: uuid.Nil,
			setupMock: func(mockService *MockClaimService) {
				// No mock setup needed as the method should return early
			},
			expectedResult: &gql_model.ClaimRewardResponse{
				ClaimMeme:     "",
				ClaimContract: "",
			},
		},
		{
			name:   "service error",
			userID: uuid.New(),
			setupMock: func(mockService *MockClaimService) {
				mockService.On("GetClaimReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(nil, errors.New("service error"))
			},
			expectedResult: &gql_model.ClaimRewardResponse{
				ClaimMeme:     "",
				ClaimContract: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := &MockClaimService{}
			tt.setupMock(mockService)

			resolver := &ClaimResolver{claimService: mockService}
			ctx := createTestContextWithUserID(tt.userID)

			// Act
			result, err := resolver.GetClaimReward(ctx)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedResult.ClaimMeme, result.ClaimMeme)
			assert.Equal(t, tt.expectedResult.ClaimContract, result.ClaimContract)

			mockService.AssertExpectations(t)
		})
	}
}

func TestClaimResolver_ClaimMemeReward(t *testing.T) {
	tests := []struct {
		name           string
		userID         uuid.UUID
		input          gql_model.ClaimMemeRewardInput
		setupMock      func(*MockClaimService)
		expectedResult *gql_model.ClaimResultResponse
	}{
		{
			name:   "successful meme reward claim",
			userID: uuid.New(),
			input:  gql_model.ClaimMemeRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				mockService.On("ClaimMemeReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(nil)
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: true,
				Message: "Claim meme reward successfully",
			},
		},
		{
			name:   "user not logged in",
			userID: uuid.Nil,
			input:  gql_model.ClaimMemeRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				// No mock setup needed as the method should return early
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: false,
				Message: "User is not logged in",
			},
		},
		{
			name:   "service error",
			userID: uuid.New(),
			input:  gql_model.ClaimMemeRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				mockService.On("ClaimMemeReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(errors.New("service error"))
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: false,
				Message: "failed claim meme reward",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := &MockClaimService{}
			tt.setupMock(mockService)

			resolver := &ClaimResolver{claimService: mockService}
			ctx := createTestContextWithUserID(tt.userID)

			// Act
			result, err := resolver.ClaimMemeReward(ctx, tt.input)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedResult.Success, result.Success)
			assert.Equal(t, tt.expectedResult.Message, result.Message)

			mockService.AssertExpectations(t)
		})
	}
}

func TestClaimResolver_ClaimContractReward(t *testing.T) {
	tests := []struct {
		name           string
		userID         uuid.UUID
		input          gql_model.ClaimContractRewardInput
		setupMock      func(*MockClaimService)
		expectedResult *gql_model.ClaimResultResponse
	}{
		{
			name:   "successful contract reward claim",
			userID: uuid.New(),
			input:  gql_model.ClaimContractRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				mockService.On("ClaimContractReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(nil)
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: true,
				Message: "Claim contract reward successfully",
			},
		},
		{
			name:   "user not logged in",
			userID: uuid.Nil,
			input:  gql_model.ClaimContractRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				// No mock setup needed as the method should return early
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: false,
				Message: "User is not logged in",
			},
		},
		{
			name:   "service error",
			userID: uuid.New(),
			input:  gql_model.ClaimContractRewardInput{},
			setupMock: func(mockService *MockClaimService) {
				mockService.On("ClaimContractReward", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(errors.New("service error"))
			},
			expectedResult: &gql_model.ClaimResultResponse{
				Success: false,
				Message: "failed claim contract reward",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockService := &MockClaimService{}
			tt.setupMock(mockService)

			resolver := &ClaimResolver{claimService: mockService}
			ctx := createTestContextWithUserID(tt.userID)

			// Act
			result, err := resolver.ClaimContractReward(ctx, tt.input)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedResult.Success, result.Success)
			assert.Equal(t, tt.expectedResult.Message, result.Message)

			mockService.AssertExpectations(t)
		})
	}
}

// Helper function to create test context with user ID
func createTestContextWithUserID(userID uuid.UUID) context.Context {
	ctx := context.Background()
	if userID != uuid.Nil {
		ctx = context.WithValue(ctx, "userId", userID.String())
	}
	return ctx
}
