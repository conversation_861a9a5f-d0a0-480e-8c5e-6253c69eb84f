package resolvers

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
)

type ClaimResolver struct {
	claimService service.ClaimI
}

func NewClaimResolver() *ClaimResolver {
	return &ClaimResolver{
		claimService: reward.NewClaimService(),
	}
}

func (r *ClaimResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimRewardResponse{
			ClaimMeme:     "",
			ClaimContract: "",
		}, nil
	}

	// Validate input
	if userID == uuid.Nil {
		return &gql_model.ClaimRewardResponse{
			ClaimMeme:     "",
			ClaimContract: "",
		}, nil
	}

	claimReward, err := r.claimService.GetClaimReward(ctx, userID)
	if err != nil {
		return &gql_model.ClaimRewardResponse{
			ClaimMeme:     "",
			ClaimContract: "",
		}, nil
	}
	return &gql_model.ClaimRewardResponse{
		ClaimMeme:     claimReward.ClaimMeme,
		ClaimContract: claimReward.ClaimContract,
	}, nil
}

// ClaimReward handles reward claiming by type
func (r *ClaimResolver) ClaimMemeReward(ctx context.Context, input gql_model.ClaimMemeRewardInput) (*gql_model.ClaimResultResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	// Validate input
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	err := r.claimService.ClaimMemeReward(ctx, userID)
	if err != nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "failed claim meme reward",
		}, nil
	}

	return &gql_model.ClaimResultResponse{
		Success: true,
		Message: "Claim meme reward successfully",
	}, nil
}

func (r *ClaimResolver) ClaimContractReward(ctx context.Context, input gql_model.ClaimContractRewardInput) (*gql_model.ClaimResultResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	// Validate input
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	err := r.claimService.ClaimContractReward(ctx, userID)
	if err != nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "failed claim contract reward",
		}, nil
	}

	return &gql_model.ClaimResultResponse{
		Success: true,
		Message: "Claim contract reward successfully",
	}, nil
}
