scalar Time

directive @auth on FIELD_DEFINITION

type Query {
  # Get referral snapshot
  referralSnapshot: ReferralSnapshot @auth

  # Get all agent levels
  agentLevels: [AgentLevel!]! @auth

  # Get specific agent level by ID
  agentLevel(id: Int!): AgentLevel @auth

  # Get transaction data (all, meme, contract)
  transactionData(input: TransactionDataInput!): TransactionDataResponse! @auth

  # Get data overview with charts
  dataOverview(input: DataOverviewInput!): DataOverviewWithSummary! @auth

  # Get user invitation data statistics
  userInvitationData: UserInvitationDataResponse! @auth

  # Get user reward data
  rewardData: RewardDataResponse! @auth

  # Get user invitation record
  invitationRecords: InvitationRecordResponse! @auth

  # Get user claim reward
  getClaimReward: ClaimRewardResponse! @auth
}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth

  # Update commission rates and meme fee rebate for a specific level
  updateLevelCommission(input: UpdateLevelCommissionInput!): UpdateLevelCommissionResponse! @auth

  # Claim rewards by meme
  claimMemeReward(input: ClaimMemeRewardInput!): ClaimResultResponse! @auth

  # Claim rewards by contract
  claimContractReward(input: ClaimContractRewardInput!): ClaimResultResponse! @auth
}
