package repo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewAffiliateRepository(t *testing.T) {
	// Act
	repo := NewAffiliateRepository()

	// Assert
	require.NotNil(t, repo)
	assert.IsType(t, &AffiliateRepository{}, repo)
}

func TestAffiliateRepository_InterfaceCompliance(t *testing.T) {
	// This test ensures that AffiliateRepository implements AffiliateRepositoryInterface
	var _ AffiliateRepositoryInterface = (*AffiliateRepository)(nil)
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestAffiliateRepository_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}

func TestAffiliateRepository_Methods(t *testing.T) {
	t.Run("constructor creates valid instance", func(t *testing.T) {
		repo := NewAffiliateRepository()
		require.NotNil(t, repo)
		
		// Verify it's the correct type
		affiliateRepo, ok := repo.(*AffiliateRepository)
		require.True(t, ok)
		require.NotNil(t, affiliateRepo)
	})
}

// Test that all interface methods are implemented
func TestAffiliateRepository_MethodSignatures(t *testing.T) {
	// This test will fail to compile if any interface methods are missing
	// It serves as a compile-time check for interface compliance
	
	repo := NewAffiliateRepository()
	require.NotNil(t, repo)
	
	// The fact that this compiles means all methods are implemented
	// with correct signatures
	assert.Implements(t, (*AffiliateRepositoryInterface)(nil), repo)
}
