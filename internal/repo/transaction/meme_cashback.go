package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// MemeCashbackRepositoryInterface defines the interface for meme cashback operations
type MemeCashbackRepositoryInterface interface {
	GetPendingCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error)
	GetClaimedCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error)
	UpdateCashbackStatusToClaimed(ctx context.Context, userID uuid.UUID, cashbackIDs []uuid.UUID) error
	GetCashbackByID(ctx context.Context, cashbackID uuid.UUID) (*model.MemeCashback, error)
}

type MemeCashbackRepository struct {
	db *gorm.DB
}

func NewMemeCashbackRepository() MemeCashbackRepositoryInterface {
	return &MemeCashbackRepository{
		db: global.GVA_DB,
	}
}

// GetPendingCashbacksByUserID gets all pending cashbacks for a user
func (r *MemeCashbackRepository) GetPendingCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error) {
	var cashbacks []model.MemeCashback
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Find(&cashbacks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending cashbacks: %w", err)
	}

	return cashbacks, nil
}

// GetClaimedCashbacksByUserID gets all claimed cashbacks for a user
func (r *MemeCashbackRepository) GetClaimedCashbacksByUserID(ctx context.Context, userID uuid.UUID) ([]model.MemeCashback, error) {
	var cashbacks []model.MemeCashback
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "CLAIMED").
		Find(&cashbacks).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get claimed cashbacks: %w", err)
	}

	return cashbacks, nil
}

// UpdateCashbackStatusToClaimed updates the status of specified cashbacks to CLAIMED
func (r *MemeCashbackRepository) UpdateCashbackStatusToClaimed(ctx context.Context, userID uuid.UUID, cashbackIDs []uuid.UUID) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&model.MemeCashback{}).
		Where("user_id = ? AND id IN ? AND status = ?", userID, cashbackIDs, "PENDING_CLAIM").
		Updates(map[string]interface{}{
			"status":     "CLAIMED",
			"claimed_at": &now,
			"updated_at": &now,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to update cashback status: %w", err)
	}

	return nil
}

// GetCashbackByID gets a specific cashback by ID
func (r *MemeCashbackRepository) GetCashbackByID(ctx context.Context, cashbackID uuid.UUID) (*model.MemeCashback, error) {
	var cashback model.MemeCashback
	err := r.db.WithContext(ctx).
		Where("id = ?", cashbackID).
		First(&cashback).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get cashback by ID: %w", err)
	}

	return &cashback, nil
}
