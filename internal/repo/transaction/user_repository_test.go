package transaction

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewUserRepository(t *testing.T) {
	// Act
	repo := NewUserRepository()

	// Assert
	require.NotNil(t, repo)
	assert.IsType(t, &UserRepository{}, repo)
}

func TestUserRepository_InterfaceCompliance(t *testing.T) {
	// This test ensures that UserRepository implements UserRepositoryInterface
	var _ UserRepositoryInterface = (*UserRepository)(nil)
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestUserRepository_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}

func TestUserRepository_Methods(t *testing.T) {
	t.Run("constructor creates valid instance", func(t *testing.T) {
		repo := NewUserRepository()
		require.NotNil(t, repo)
		
		// Verify it's the correct type
		userRepo, ok := repo.(*UserRepository)
		require.True(t, ok)
		require.NotNil(t, userRepo)
	})
}

// Test that all interface methods are implemented
func TestUserRepository_MethodSignatures(t *testing.T) {
	// This test will fail to compile if any interface methods are missing
	// It serves as a compile-time check for interface compliance
	
	repo := NewUserRepository()
	require.NotNil(t, repo)
	
	// The fact that this compiles means all methods are implemented
	// with correct signatures
	assert.Implements(t, (*UserRepositoryInterface)(nil), repo)
}
