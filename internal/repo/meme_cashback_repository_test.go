package repo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewMemeCashbackRepository(t *testing.T) {
	// Act
	repo := NewMemeCashbackRepository()

	// Assert
	require.NotNil(t, repo)
	assert.IsType(t, &MemeCashbackRepository{}, repo)
}

func TestMemeCashbackRepository_InterfaceCompliance(t *testing.T) {
	// This test ensures that MemeCashbackRepository implements MemeCashbackRepositoryInterface
	var _ MemeCashbackRepositoryInterface = (*MemeCashbackRepository)(nil)
}

// Note: Database-dependent tests are skipped in unit tests
// These should be covered by integration tests with a real database
func TestMemeCashbackRepository_DatabaseOperations(t *testing.T) {
	t.Skip("Skipping database tests - these require integration test setup with real database")
}

func TestMemeCashbackRepository_Methods(t *testing.T) {
	t.Run("constructor creates valid instance", func(t *testing.T) {
		repo := NewMemeCashbackRepository()
		require.NotNil(t, repo)
		
		// Verify it's the correct type
		cashbackRepo, ok := repo.(*MemeCashbackRepository)
		require.True(t, ok)
		require.NotNil(t, cashbackRepo)
	})
}

// Test that all interface methods are implemented
func TestMemeCashbackRepository_MethodSignatures(t *testing.T) {
	// This test will fail to compile if any interface methods are missing
	// It serves as a compile-time check for interface compliance
	
	repo := NewMemeCashbackRepository()
	require.NotNil(t, repo)
	
	// The fact that this compiles means all methods are implemented
	// with correct signatures
	assert.Implements(t, (*MemeCashbackRepositoryInterface)(nil), repo)
}
